.btn,
.navbar .navbar-nav > li > a.btn{
    border-radius: $border-radius-btn-base;
    box-sizing: border-box;
    border-width: $border-thick;
    background-color: $transparent-bg;
    font-size: $font-size-base;
    font-weight: $font-weight-semi;
    
    padding: $padding-base-vertical $padding-base-horizontal;
    
    @include btn-styles($default-color, $default-states-color);
    @include transition($fast-transition-time, linear);
    
    &:hover,
    &:focus{
        outline: 0 !important;
    }
    &:active,
    &.active,
    .open > &.dropdown-toggle {
         @include box-shadow(none);
         outline: 0 !important;
    }
    
    &.btn-icon{
        padding: $padding-base-vertical;
    } 
}

.btn-group .btn + .btn, 
.btn-group .btn + .btn-group, 
.btn-group .btn-group + .btn, 
.btn-group .btn-group + .btn-group{
    margin-left: -2px;
}

// Apply the mixin to the buttons
//.btn-default { @include btn-styles($default-color, $default-states-color); }
.navbar .navbar-nav > li > a.btn-primary, .btn-primary { @include btn-styles($primary-color, $primary-states-color); }
.navbar .navbar-nav > li > a.btn-success, .btn-success { @include btn-styles($success-color, $success-states-color); }
.navbar .navbar-nav > li > a.btn-info, .btn-info    { @include btn-styles($info-color, $info-states-color); }
.navbar .navbar-nav > li > a.btn-warning, .btn-warning { @include btn-styles($warning-color, $warning-states-color); }
.navbar .navbar-nav > li > a.btn-danger, .btn-danger  { @include btn-styles($danger-color, $danger-states-color); }
.btn-neutral { 
    @include btn-styles($white-color, $white-color);
    
    &:hover,
    &:focus{
        color: $default-color;
    }
    
    &:active,
    &.active,
    .open > &.dropdown-toggle{
         background-color: $white-color;
         color: $default-color;
    }    
    
    &.btn-fill{
        color: $default-color;
    }
    &.btn-fill:hover,
    &.btn-fill:focus{
        color: $default-states-color;
    }
    
    &.btn-simple:active,
    &.btn-simple.active{
        background-color: transparent;
    }
}

.btn{
     &:disabled,
     &[disabled],
     &.disabled{
        @include opacity(.5);
    }
}
.btn-simple{
    border: $none;
    padding: $padding-base-vertical $padding-base-horizontal;
    
    &.btn-icon{
        padding: $padding-base-vertical;
    }
}
.btn-lg{
   @include btn-size($padding-large-vertical, $padding-large-horizontal, $font-size-large, $border-radius-btn-large, $line-height-small);
   font-weight: $font-weight-normal;
}
.btn-sm{
    @include btn-size($padding-small-vertical, $padding-small-horizontal, $font-size-small, $border-radius-btn-small, $line-height-small);    
}
.btn-xs {
    @include btn-size($padding-xs-vertical, $padding-xs-horizontal, $font-size-xs, $border-radius-btn-small, $line-height-small);
}
.btn-wd {
    min-width: 140px;
}

.btn-group.select{
    width: 100%;
}
.btn-group.select .btn{
    text-align: left;
}
.btn-group.select .caret{
    position: absolute;
    top: 50%;
    margin-top: -1px;
    right: 8px;
}
