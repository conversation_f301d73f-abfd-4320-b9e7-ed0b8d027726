<?php
/**
 * Test script for Leave Management Admin System
 * This script tests admin functionality for leave approval and management
 */

// Include the User class
include('class/User.php');

// Start output buffering for clean display
ob_start();

echo "<h2>Leave Management Admin System Test Results</h2>";
echo "<hr>";

// Test 1: Database Connection
echo "<h3>1. Database Connection Test</h3>";
try {
    $user = new User();
    echo "<span style='color: green;'>✓ Database connection successful</span><br>";
} catch (Exception $e) {
    echo "<span style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</span><br>";
    exit();
}

// Test 2: Admin Authentication
echo "<h3>2. Admin Authentication Test</h3>";
session_start();

if (isset($_SESSION['adminUserid'])) {
    echo "<span style='color: green;'>✓ Admin session active (Admin ID: " . $_SESSION['adminUserid'] . ")</span><br>";
    
    // Get admin details
    try {
        $adminDetails = $user->adminDetails();
        if ($adminDetails) {
            echo "<span style='color: blue;'>→ Admin: " . $adminDetails['first_name'] . " " . $adminDetails['last_name'] . "</span><br>";
            echo "<span style='color: blue;'>→ Email: " . $adminDetails['email'] . "</span><br>";
            echo "<span style='color: blue;'>→ Designation: " . $adminDetails['designation_name'] . "</span><br>";
        }
    } catch (Exception $e) {
        echo "<span style='color: red;'>✗ Failed to get admin details: " . $e->getMessage() . "</span><br>";
    }
} else {
    echo "<span style='color: orange;'>⚠ No active admin session (testing with anonymous access)</span><br>";
}

// Test 3: Admin Leave Methods
echo "<h3>3. Admin Leave Methods Test</h3>";

// Test getAllLeaveApplications method
try {
    $allApplications = $user->getAllLeaveApplications();
    echo "<span style='color: green;'>✓ getAllLeaveApplications() method working</span><br>";
    echo "<span style='color: blue;'>→ Found " . count($allApplications) . " total leave applications</span><br>";
    
    // Test with status filter
    $pendingApplications = $user->getAllLeaveApplications('pending');
    echo "<span style='color: blue;'>→ Found " . count($pendingApplications) . " pending applications</span><br>";
    
    $approvedApplications = $user->getAllLeaveApplications('approved');
    echo "<span style='color: blue;'>→ Found " . count($approvedApplications) . " approved applications</span><br>";
    
} catch (Exception $e) {
    echo "<span style='color: red;'>✗ getAllLeaveApplications() method failed: " . $e->getMessage() . "</span><br>";
}

// Test getLeaveStatistics method
try {
    $leaveStatistics = $user->getLeaveStatistics();
    echo "<span style='color: green;'>✓ getLeaveStatistics() method working</span><br>";
    echo "<span style='color: blue;'>→ Generated statistics for " . count($leaveStatistics) . " records</span><br>";
} catch (Exception $e) {
    echo "<span style='color: red;'>✗ getLeaveStatistics() method failed: " . $e->getMessage() . "</span><br>";
}

// Test 4: File Structure for Admin
echo "<h3>4. Admin File Structure Test</h3>";

$requiredAdminFiles = [
    'staff_leave.php',
    'leave_details.php',
    'leave_reports.php',
    'CSS/leave-admin.css',
    'JS/leave-admin.js'
];

foreach ($requiredAdminFiles as $file) {
    if (file_exists($file)) {
        echo "<span style='color: green;'>✓ " . $file . " exists</span><br>";
    } else {
        echo "<span style='color: red;'>✗ " . $file . " missing</span><br>";
    }
}

// Test 5: Menu Integration for Admin
echo "<h3>5. Admin Menu Integration Test</h3>";

if (file_exists('side-menu.php')) {
    $menuContent = file_get_contents('side-menu.php');
    if (strpos($menuContent, 'staff_leave.php') !== false) {
        echo "<span style='color: green;'>✓ Staff Leave menu item found in side-menu.php</span><br>";
    } else {
        echo "<span style='color: orange;'>⚠ Staff Leave menu item not found in side-menu.php</span><br>";
    }
    
    if (strpos($menuContent, 'leave_reports.php') !== false) {
        echo "<span style='color: green;'>✓ Leave Reports menu item found in side-menu.php</span><br>";
    } else {
        echo "<span style='color: orange;'>⚠ Leave Reports menu item not found in side-menu.php</span><br>";
    }
} else {
    echo "<span style='color: red;'>✗ side-menu.php not found</span><br>";
}

// Test 6: Database Relationships
echo "<h3>6. Database Relationships Test</h3>";

// Test foreign key relationships
$query = "SELECT 
    sl.leave_id,
    sl.staff_id,
    u.first_name,
    u.last_name,
    lt.leave_type_name
FROM staff_leave sl
LEFT JOIN user u ON sl.staff_id = u.id
LEFT JOIN leave_type lt ON sl.leave_type_id = lt.leave_type_id
LIMIT 5";

$result = mysqli_query($user->dbConnect, $query);
if ($result && mysqli_num_rows($result) > 0) {
    echo "<span style='color: green;'>✓ Database relationships working correctly</span><br>";
    echo "<span style='color: blue;'>→ Sample data with proper joins:</span><br>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<span style='color: blue;'>  - Leave #" . $row['leave_id'] . ": " . $row['first_name'] . " " . $row['last_name'] . " - " . $row['leave_type_name'] . "</span><br>";
    }
} else {
    echo "<span style='color: orange;'>⚠ No leave applications found or relationship issues</span><br>";
}

// Test 7: User Permissions
echo "<h3>7. User Permissions Test</h3>";

// Check staff users
$query = "SELECT COUNT(*) as count FROM user WHERE designation IN (1,2,3) AND status = 'active'";
$result = mysqli_query($user->dbConnect, $query);
$row = mysqli_fetch_assoc($result);
echo "<span style='color: blue;'>→ Found " . $row['count'] . " active staff members (admin, staff, teacher)</span><br>";

// Check admin users
$query = "SELECT COUNT(*) as count FROM user WHERE designation = 1 AND status = 'active'";
$result = mysqli_query($user->dbConnect, $query);
$row = mysqli_fetch_assoc($result);
echo "<span style='color: blue;'>→ Found " . $row['count'] . " active admin users</span><br>";

if ($row['count'] > 0) {
    echo "<span style='color: green;'>✓ Admin users available for leave approval</span><br>";
} else {
    echo "<span style='color: red;'>✗ No admin users found for leave approval</span><br>";
}

// Test 8: Leave Type Configuration
echo "<h3>8. Leave Type Configuration Test</h3>";

$query = "SELECT * FROM leave_type WHERE is_active = 1 ORDER BY leave_type_name";
$result = mysqli_query($user->dbConnect, $query);

if ($result && mysqli_num_rows($result) > 0) {
    echo "<span style='color: green;'>✓ Leave types configured properly</span><br>";
    echo "<span style='color: blue;'>→ Available leave types:</span><br>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<span style='color: blue;'>  - " . $row['leave_type_name'] . " (Max: " . $row['max_days_per_year'] . " days/year)</span><br>";
    }
} else {
    echo "<span style='color: red;'>✗ No active leave types found</span><br>";
}

// Test 9: Sample Leave Application Processing
echo "<h3>9. Leave Application Processing Test</h3>";

// Check if there are any pending applications to test with
$query = "SELECT COUNT(*) as count FROM staff_leave WHERE status = 'pending'";
$result = mysqli_query($user->dbConnect, $query);
$row = mysqli_fetch_assoc($result);

if ($row['count'] > 0) {
    echo "<span style='color: green;'>✓ Found " . $row['count'] . " pending applications for testing</span><br>";
    echo "<span style='color: blue;'>→ Admin can test approval/rejection workflow</span><br>";
} else {
    echo "<span style='color: orange;'>⚠ No pending applications found (create test applications to verify workflow)</span><br>";
}

// Test Summary
echo "<h3>Test Summary</h3>";
echo "<hr>";

$content = ob_get_contents();
$successCount = substr_count($content, '✓');
$warningCount = substr_count($content, '⚠');
$errorCount = substr_count($content, '✗');

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
echo "<strong>Admin Test Results:</strong><br>";
echo "<span style='color: green;'>✓ Passed: " . $successCount . "</span><br>";
echo "<span style='color: orange;'>⚠ Warnings: " . $warningCount . "</span><br>";
echo "<span style='color: red;'>✗ Failed: " . $errorCount . "</span><br>";

if ($errorCount == 0) {
    echo "<br><span style='color: green; font-weight: bold;'>🎉 All critical admin tests passed! The Leave Management Admin System is ready for use.</span>";
} else {
    echo "<br><span style='color: red; font-weight: bold;'>❌ Some admin tests failed. Please review and fix the issues above.</span>";
}
echo "</div>";

// Admin Usage Instructions
echo "<h3>Admin Usage Instructions</h3>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff;'>";
echo "<strong>To use the admin system:</strong><br>";
echo "1. Log in as an administrator (designation = 1)<br>";
echo "2. Navigate to 'Staff Leave' in the side menu<br>";
echo "3. Review pending applications and approve/reject as needed<br>";
echo "4. Use 'Leave Reports' to view statistics and generate reports<br>";
echo "5. Monitor leave balances and usage patterns<br>";
echo "</div>";

// Testing Workflow
echo "<h3>Testing Workflow</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107;'>";
echo "<strong>Recommended testing steps:</strong><br>";
echo "1. Create test leave applications as a staff member<br>";
echo "2. Log in as admin and review applications<br>";
echo "3. Test approval and rejection workflows<br>";
echo "4. Verify email notifications (if configured)<br>";
echo "5. Generate and export reports<br>";
echo "6. Test different leave types and date ranges<br>";
echo "</div>";

ob_end_flush();
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background: #f8f9fa;
}
h2, h3 {
    color: #495057;
}
hr {
    border: 1px solid #dee2e6;
}
code {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: monospace;
}
</style>
