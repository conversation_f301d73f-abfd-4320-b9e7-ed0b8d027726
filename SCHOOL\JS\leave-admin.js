/**
 * Leave Administration JavaScript Functions for School Module
 * Handles admin approval workflows, reporting, and data management
 */

// Initialize when document is ready
$(document).ready(function() {
    initializeAdminFunctions();
    initializeDataTables();
    initializeModals();
    initializeFilters();
    initializeAnimations();
});

/**
 * Initialize admin-specific functions
 */
function initializeAdminFunctions() {
    // Auto-refresh pending applications count
    updatePendingCount();
    setInterval(updatePendingCount, 300000); // Every 5 minutes
    
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // Initialize confirmation dialogs
    initializeConfirmations();
}

/**
 * Initialize DataTables for better data management
 */
function initializeDataTables() {
    if ($('#leaveApplicationsTable').length) {
        $('#leaveApplicationsTable').DataTable({
            responsive: true,
            pageLength: 25,
            order: [[0, 'desc']], // Order by application date
            columnDefs: [
                { orderable: false, targets: -1 } // Disable sorting on action column
            ],
            language: {
                search: "Search applications:",
                lengthMenu: "Show _MENU_ applications per page",
                info: "Showing _START_ to _END_ of _TOTAL_ applications",
                paginate: {
                    first: "First",
                    last: "Last",
                    next: "Next",
                    previous: "Previous"
                }
            }
        });
    }
    
    if ($('#leaveReportsTable').length) {
        $('#leaveReportsTable').DataTable({
            responsive: true,
            pageLength: 50,
            dom: 'Bfrtip',
            buttons: [
                'copy', 'csv', 'excel', 'pdf', 'print'
            ]
        });
    }
}

/**
 * Initialize modal dialogs
 */
function initializeModals() {
    // Process leave modal
    $('#processLeaveModal').on('show.bs.modal', function(e) {
        var button = $(e.relatedTarget);
        var leaveId = button.data('leave-id');
        var action = button.data('action');
        var staffName = button.data('staff-name');
        
        var modal = $(this);
        modal.find('#leave_id').val(leaveId);
        modal.find('#action').val(action);
        
        var title = action === 'approved' ? 'Approve Leave Application' : 'Reject Leave Application';
        var btnClass = action === 'approved' ? 'btn-success' : 'btn-danger';
        var btnText = action === 'approved' ? 'Approve' : 'Reject';
        
        modal.find('.modal-title').text(title + ' - ' + staffName);
        modal.find('#confirmBtn').removeClass('btn-success btn-danger').addClass(btnClass).text(btnText);
    });
    
    // Clear modal data when closed
    $('.modal').on('hidden.bs.modal', function() {
        $(this).find('form')[0].reset();
        $(this).find('.alert').remove();
    });
}

/**
 * Initialize filter functionality
 */
function initializeFilters() {
    // Status filter
    $('.status-filter').on('click', function(e) {
        e.preventDefault();
        var status = $(this).data('status');
        filterApplicationsByStatus(status);
        
        // Update active state
        $('.status-filter').removeClass('active');
        $(this).addClass('active');
    });
    
    // Date range filter
    $('#dateRangeFilter').on('change', function() {
        var range = $(this).val();
        filterApplicationsByDateRange(range);
    });
    
    // Staff filter
    $('#staffFilter').on('change', function() {
        var staffId = $(this).val();
        filterApplicationsByStaff(staffId);
    });
}

/**
 * Filter applications by status
 */
function filterApplicationsByStatus(status) {
    if (status === 'all') {
        $('.application-card').show();
    } else {
        $('.application-card').hide();
        $('.application-card').each(function() {
            if ($(this).find('.status-' + status).length > 0) {
                $(this).show();
            }
        });
    }
    
    updateFilterCount();
}

/**
 * Filter applications by date range
 */
function filterApplicationsByDateRange(range) {
    var now = new Date();
    var filterDate = new Date();
    
    switch(range) {
        case 'today':
            filterDate.setHours(0, 0, 0, 0);
            break;
        case 'week':
            filterDate.setDate(now.getDate() - 7);
            break;
        case 'month':
            filterDate.setMonth(now.getMonth() - 1);
            break;
        case 'quarter':
            filterDate.setMonth(now.getMonth() - 3);
            break;
        default:
            $('.application-card').show();
            return;
    }
    
    $('.application-card').each(function() {
        var appliedDate = new Date($(this).data('applied-date'));
        if (appliedDate >= filterDate) {
            $(this).show();
        } else {
            $(this).hide();
        }
    });
    
    updateFilterCount();
}

/**
 * Update filter count display
 */
function updateFilterCount() {
    var visibleCount = $('.application-card:visible').length;
    var totalCount = $('.application-card').length;
    
    $('#filterCount').text('Showing ' + visibleCount + ' of ' + totalCount + ' applications');
}

/**
 * Process leave application
 */
function processLeaveApplication(leaveId, action, comments) {
    showLoading();
    
    $.ajax({
        url: 'process_leave.php',
        method: 'POST',
        data: {
            leave_id: leaveId,
            action: action,
            admin_comments: comments
        },
        success: function(response) {
            hideLoading();
            
            if (response.success) {
                showAlert('Leave application ' + action + ' successfully', 'success');
                
                // Update the application card
                updateApplicationCard(leaveId, action, response.data);
                
                // Close modal
                $('#processLeaveModal').modal('hide');
                
                // Update counts
                updatePendingCount();
                
            } else {
                showAlert('Error processing application: ' + response.message, 'danger');
            }
        },
        error: function() {
            hideLoading();
            showAlert('Error processing application. Please try again.', 'danger');
        }
    });
}

/**
 * Update application card after processing
 */
function updateApplicationCard(leaveId, action, data) {
    var card = $('.application-card[data-leave-id="' + leaveId + '"]');
    
    // Update status
    var statusElement = card.find('.status-indicator');
    statusElement.removeClass('status-pending status-approved status-rejected')
               .addClass('status-' + action)
               .text(action.charAt(0).toUpperCase() + action.slice(1));
    
    // Remove action buttons
    card.find('.action-buttons').remove();
    
    // Add processed info
    var processedInfo = '<div class="processed-info" style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 6px;">' +
                       '<small class="text-muted">Processed on ' + new Date().toLocaleDateString() + ' by ' + data.admin_name + '</small>' +
                       '</div>';
    card.append(processedInfo);
    
    // Add animation
    card.addClass('fade-in-up');
}

/**
 * Update pending applications count
 */
function updatePendingCount() {
    $.ajax({
        url: 'get_pending_count.php',
        method: 'GET',
        success: function(response) {
            if (response.count !== undefined) {
                $('#pendingCount').text(response.count);
                
                // Update badge
                if (response.count > 0) {
                    $('#pendingBadge').text(response.count).show();
                } else {
                    $('#pendingBadge').hide();
                }
            }
        }
    });
}

/**
 * Initialize confirmation dialogs
 */
function initializeConfirmations() {
    $('.confirm-action').on('click', function(e) {
        var message = $(this).data('confirm-message') || 'Are you sure you want to perform this action?';
        
        if (!confirm(message)) {
            e.preventDefault();
            return false;
        }
    });
}

/**
 * Initialize animations
 */
function initializeAnimations() {
    // Stagger animation for cards
    $('.application-card, .report-card').each(function(index) {
        $(this).css('animation-delay', (index * 0.1) + 's').addClass('fade-in-up');
    });
    
    // Hover effects
    $('.application-card').hover(
        function() { $(this).addClass('shadow-lg'); },
        function() { $(this).removeClass('shadow-lg'); }
    );
}

/**
 * Export data functions
 */
function exportApplicationsToCSV() {
    var data = [];
    
    $('.application-card:visible').each(function() {
        var card = $(this);
        data.push({
            'Staff Name': card.find('.staff-info').text().trim(),
            'Email': card.find('.staff-email').text().trim(),
            'Leave Type': card.find('.leave-type').text().trim(),
            'Start Date': card.find('.start-date').text().trim(),
            'End Date': card.find('.end-date').text().trim(),
            'Total Days': card.find('.total-days').text().trim(),
            'Status': card.find('.status-indicator').text().trim(),
            'Applied Date': card.find('.applied-date').text().trim()
        });
    });
    
    exportToCSV(data, 'leave_applications_' + new Date().toISOString().split('T')[0] + '.csv');
}

function exportReportsToCSV() {
    var data = [];
    
    $('.report-card').each(function() {
        var card = $(this);
        var staffName = card.find('.staff-report-header').text().trim();
        
        card.find('.leave-type-summary').each(function() {
            var summary = $(this);
            data.push({
                'Staff Name': staffName,
                'Leave Type': summary.find('.leave-type-name').text().trim(),
                'Used Days': summary.find('.used-days').text().replace(/\D/g, ''),
                'Pending Days': summary.find('.pending-days').text().replace(/\D/g, ''),
                'Remaining Days': summary.find('.remaining-days').text().replace(/\D/g, '')
            });
        });
    });
    
    exportToCSV(data, 'leave_reports_' + new Date().toISOString().split('T')[0] + '.csv');
}

/**
 * Utility function to export data to CSV
 */
function exportToCSV(data, filename) {
    if (data.length === 0) {
        showAlert('No data to export', 'warning');
        return;
    }
    
    var csv = '';
    
    // Add headers
    csv += Object.keys(data[0]).join(',') + '\n';
    
    // Add data rows
    data.forEach(function(row) {
        csv += Object.values(row).map(function(value) {
            return '"' + String(value).replace(/"/g, '""') + '"';
        }).join(',') + '\n';
    });
    
    // Download CSV
    var blob = new Blob([csv], { type: 'text/csv' });
    var url = window.URL.createObjectURL(blob);
    var a = document.createElement('a');
    a.setAttribute('hidden', '');
    a.setAttribute('href', url);
    a.setAttribute('download', filename);
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    showAlert('Data exported successfully', 'success');
}

/**
 * Show/hide loading overlay
 */
function showLoading() {
    if ($('.loading-overlay').length === 0) {
        $('body').append('<div class="loading-overlay"><div class="loading-spinner"></div></div>');
    }
}

function hideLoading() {
    $('.loading-overlay').remove();
}

/**
 * Show alert messages
 */
function showAlert(message, type) {
    type = type || 'info';
    
    var alertHtml = '<div class="alert alert-' + type + ' alert-dismissible fade-in-up" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">' +
                   '<button type="button" class="close" data-dismiss="alert">&times;</button>' +
                   message + '</div>';
    
    $('body').append(alertHtml);
    
    // Auto-dismiss after 5 seconds
    setTimeout(function() {
        $('.alert-dismissible').fadeOut(function() {
            $(this).remove();
        });
    }, 5000);
}

/**
 * Print current page
 */
function printPage() {
    window.print();
}

/**
 * Refresh page data
 */
function refreshData() {
    location.reload();
}
