/**
 * Leave Management JavaScript Functions
 * Handles form validation, date calculations, and UI interactions
 */

// Initialize when document is ready
$(document).ready(function() {
    initializeLeaveForms();
    initializeDatePickers();
    initializeValidation();
    initializeAnimations();
});

/**
 * Initialize leave application forms
 */
function initializeLeaveForms() {
    // Set minimum dates to today
    var today = new Date().toISOString().split('T')[0];
    $('#start_date, #end_date').attr('min', today);
    
    // Calculate total days when dates change
    $('#start_date, #end_date').on('change', calculateLeaveDays);
    
    // Validate end date is after start date
    $('#end_date').on('change', validateDateRange);
    
    // Auto-resize textarea
    $('textarea').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
}

/**
 * Initialize date pickers with restrictions
 */
function initializeDatePickers() {
    // Disable weekends for certain leave types (optional)
    $('#leave_type_id').on('change', function() {
        var leaveType = $(this).find('option:selected').text().toLowerCase();
        
        // You can add logic here to disable weekends for certain leave types
        if (leaveType.includes('sick')) {
            // Allow all days for sick leave
        } else {
            // For other leave types, you might want to restrict weekends
        }
    });
}

/**
 * Calculate total leave days
 */
function calculateLeaveDays() {
    var startDate = $('#start_date').val();
    var endDate = $('#end_date').val();
    
    if (startDate && endDate) {
        var start = new Date(startDate);
        var end = new Date(endDate);
        
        if (end >= start) {
            var timeDiff = end.getTime() - start.getTime();
            var daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
            
            // Display calculated days
            displayCalculatedDays(daysDiff);
            
            // Check against available balance
            checkLeaveBalance(daysDiff);
        }
    }
}

/**
 * Display calculated days to user
 */
function displayCalculatedDays(days) {
    // Remove existing display
    $('.days-display').remove();
    
    // Add new display
    var display = $('<div class="days-display alert alert-info" style="margin-top: 10px;">' +
                   '<i class="fa fa-calculator"></i> Total Days: <strong>' + days + '</strong> day' + 
                   (days > 1 ? 's' : '') + '</div>');
    
    $('#end_date').closest('.form-group').after(display);
}

/**
 * Validate date range
 */
function validateDateRange() {
    var startDate = new Date($('#start_date').val());
    var endDate = new Date($('#end_date').val());
    
    if (endDate < startDate) {
        showAlert('End date cannot be before start date', 'warning');
        $('#end_date').val('');
    }
}

/**
 * Check leave balance against requested days
 */
function checkLeaveBalance(requestedDays) {
    var leaveTypeId = $('#leave_type_id').val();
    
    if (leaveTypeId) {
        // You can implement AJAX call here to check real-time balance
        // For now, we'll use client-side validation based on displayed stats
        
        var statsCards = $('.stats-card');
        var selectedLeaveType = $('#leave_type_id option:selected').text();
        
        statsCards.each(function() {
            var cardHeader = $(this).find('.stats-header').text();
            if (cardHeader.includes(selectedLeaveType.split('(')[0].trim())) {
                var remainingText = $(this).find('.balance-remaining').text();
                var remaining = parseInt(remainingText.match(/\d+/)[0]);
                
                if (requestedDays > remaining) {
                    showAlert('Warning: Requested days (' + requestedDays + ') exceed remaining balance (' + remaining + ')', 'warning');
                }
            }
        });
    }
}

/**
 * Initialize form validation
 */
function initializeValidation() {
    $('form').on('submit', function(e) {
        var isValid = true;
        var errors = [];
        
        // Validate required fields
        $(this).find('[required]').each(function() {
            if (!$(this).val().trim()) {
                isValid = false;
                errors.push($(this).prev('label').text() + ' is required');
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        
        // Validate date range
        var startDate = $('#start_date').val();
        var endDate = $('#end_date').val();
        
        if (startDate && endDate && new Date(endDate) < new Date(startDate)) {
            isValid = false;
            errors.push('End date must be after start date');
        }
        
        // Validate file upload
        var fileInput = $('#attachment')[0];
        if (fileInput && fileInput.files.length > 0) {
            var file = fileInput.files[0];
            var maxSize = 5 * 1024 * 1024; // 5MB
            var allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png'];
            
            if (file.size > maxSize) {
                isValid = false;
                errors.push('File size must be less than 5MB');
            }
            
            if (!allowedTypes.includes(file.type)) {
                isValid = false;
                errors.push('Invalid file type. Please upload PDF, DOC, DOCX, JPG, or PNG files only');
            }
        }
        
        if (!isValid) {
            e.preventDefault();
            showAlert(errors.join('<br>'), 'danger');
        }
    });
}

/**
 * Initialize animations
 */
function initializeAnimations() {
    // Fade in cards on page load
    $('.leave-card, .stats-card, .dashboard-card').addClass('fade-in');
    
    // Hover effects
    $('.leave-card').hover(
        function() { $(this).addClass('shadow-lg'); },
        function() { $(this).removeClass('shadow-lg'); }
    );
    
    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(e) {
        e.preventDefault();
        var target = $($(this).attr('href'));
        if (target.length) {
            $('html, body').animate({
                scrollTop: target.offset().top - 100
            }, 500);
        }
    });
}

/**
 * Show alert messages
 */
function showAlert(message, type) {
    type = type || 'info';
    
    var alertHtml = '<div class="alert alert-' + type + ' alert-dismissible fade-in" style="margin-top: 15px;">' +
                   '<button type="button" class="close" data-dismiss="alert">&times;</button>' +
                   message + '</div>';
    
    // Remove existing alerts
    $('.alert').not('.alert-permanent').remove();
    
    // Add new alert
    $('form').prepend(alertHtml);
    
    // Auto-dismiss after 5 seconds
    setTimeout(function() {
        $('.alert-dismissible').not('.alert-permanent').fadeOut();
    }, 5000);
}

/**
 * Cancel leave application
 */
function cancelLeave(leaveId) {
    if (confirm('Are you sure you want to cancel this leave application?')) {
        // Show loading state
        var button = $('button[onclick*="' + leaveId + '"]');
        button.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Cancelling...');
        
        // AJAX call to cancel leave
        $.ajax({
            url: 'cancel_leave.php',
            method: 'POST',
            data: { leave_id: leaveId },
            success: function(response) {
                if (response.success) {
                    showAlert('Leave application cancelled successfully', 'success');
                    // Reload page after 2 seconds
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    showAlert('Error cancelling leave application: ' + response.message, 'danger');
                    button.prop('disabled', false).html('<i class="fa fa-ban"></i> Cancel Application');
                }
            },
            error: function() {
                showAlert('Error cancelling leave application. Please try again.', 'danger');
                button.prop('disabled', false).html('<i class="fa fa-ban"></i> Cancel Application');
            }
        });
    }
}

/**
 * Filter leave applications
 */
function filterLeaves(status) {
    $('.leave-card').hide();
    
    if (status === 'all') {
        $('.leave-card').show();
    } else {
        $('.leave-card').each(function() {
            if ($(this).find('.status-' + status).length > 0) {
                $(this).show();
            }
        });
    }
    
    // Update active filter button
    $('.filter-btn').removeClass('active');
    $('.filter-btn[data-status="' + status + '"]').addClass('active');
}

/**
 * Export data to CSV
 */
function exportToCSV(data, filename) {
    var csv = '';
    
    // Add headers
    if (data.length > 0) {
        csv += Object.keys(data[0]).join(',') + '\n';
        
        // Add data rows
        data.forEach(function(row) {
            csv += Object.values(row).map(function(value) {
                return '"' + String(value).replace(/"/g, '""') + '"';
            }).join(',') + '\n';
        });
    }
    
    // Download CSV
    var blob = new Blob([csv], { type: 'text/csv' });
    var url = window.URL.createObjectURL(blob);
    var a = document.createElement('a');
    a.setAttribute('hidden', '');
    a.setAttribute('href', url);
    a.setAttribute('download', filename || 'leave_data.csv');
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
}

/**
 * Print current page
 */
function printPage() {
    window.print();
}

/**
 * Utility function to format dates
 */
function formatDate(dateString) {
    var date = new Date(dateString);
    var options = { year: 'numeric', month: 'long', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
}

/**
 * Utility function to calculate business days
 */
function calculateBusinessDays(startDate, endDate) {
    var start = new Date(startDate);
    var end = new Date(endDate);
    var businessDays = 0;
    
    while (start <= end) {
        var dayOfWeek = start.getDay();
        if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Not Sunday (0) or Saturday (6)
            businessDays++;
        }
        start.setDate(start.getDate() + 1);
    }
    
    return businessDays;
}

/**
 * Show loading spinner
 */
function showLoading(element) {
    $(element).addClass('loading');
}

/**
 * Hide loading spinner
 */
function hideLoading(element) {
    $(element).removeClass('loading');
}
