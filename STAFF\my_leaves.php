<?php 
include('class/User.php');
$user = new User();
$user->loginStatus();

$myLeaves = $user->getStaffLeaves();

?>
<!DOCTYPE html>
<html>
<head>
	<title>My Leave Applications - Staff Portal</title>
	<?php include('include/header.php'); ?>
	<link rel="stylesheet" href="css/dataTables.bootstrap4.min.css" />
	<style>
		.status-pending { color: #ffc107; font-weight: bold; }
		.status-approved { color: #28a745; font-weight: bold; }
		.status-rejected { color: #dc3545; font-weight: bold; }
		.status-cancelled { color: #6c757d; font-weight: bold; }
		.panel-heading {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
		.leave-details {
			background: #f8f9fa;
			padding: 15px;
			border-radius: 8px;
			margin-bottom: 15px;
		}
		.leave-card {
			border: 1px solid #dee2e6;
			border-radius: 8px;
			padding: 15px;
			margin-bottom: 15px;
			background: #fff;
		}
		.leave-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 10px;
		}
		.leave-dates {
			font-size: 14px;
			color: #6c757d;
		}
		.attachment-link {
			color: #007bff;
			text-decoration: none;
		}
		.attachment-link:hover {
			text-decoration: underline;
		}
	</style>
</head>
<body>
	<?php include('include/container.php');?>
	<div class="container contact">
		<h2>Staff Leave Management System - My Leave Applications</h2>

		<?php include('staffmenu.php'); ?>
		<div class="col-lg-10 col-md-10 col-sm-9 col-xs-12">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h3><i class="fa fa-list"></i> My Leave Applications</h3>
					<div class="pull-right">
						<a href="apply_leave.php" class="btn btn-primary btn-sm">
							<i class="fa fa-plus"></i> Apply for Leave
						</a>
					</div>
				</div>
				<div class="panel-body">
					<?php if(empty($myLeaves)) { ?>
						<div class="alert alert-info">
							<i class="fa fa-info-circle"></i> You have not submitted any leave applications yet.
							<a href="apply_leave.php" class="btn btn-primary btn-sm pull-right">Apply Now</a>
						</div>
					<?php } else { ?>
						<div class="row">
							<?php foreach($myLeaves as $leave) { 
								$statusClass = 'status-' . $leave['status'];
								$statusIcon = '';
								switch($leave['status']) {
									case 'pending': $statusIcon = 'fa-clock-o'; break;
									case 'approved': $statusIcon = 'fa-check-circle'; break;
									case 'rejected': $statusIcon = 'fa-times-circle'; break;
									case 'cancelled': $statusIcon = 'fa-ban'; break;
								}
							?>
							<div class="col-md-6">
								<div class="leave-card">
									<div class="leave-header">
										<h5><?php echo $leave['leave_type_name']; ?></h5>
										<span class="<?php echo $statusClass; ?>">
											<i class="fa <?php echo $statusIcon; ?>"></i>
											<?php echo ucfirst($leave['status']); ?>
										</span>
									</div>
									
									<div class="leave-dates">
										<i class="fa fa-calendar"></i>
										<?php echo date('M d, Y', strtotime($leave['start_date'])); ?> - 
										<?php echo date('M d, Y', strtotime($leave['end_date'])); ?>
										(<?php echo $leave['total_days']; ?> day<?php echo $leave['total_days'] > 1 ? 's' : ''; ?>)
									</div>
									
									<div style="margin: 10px 0;">
										<strong>Reason:</strong><br>
										<?php echo nl2br(htmlspecialchars($leave['reason'])); ?>
									</div>
									
									<?php if($leave['emergency_contact']) { ?>
									<div style="margin: 10px 0;">
										<strong>Emergency Contact:</strong><br>
										<?php echo htmlspecialchars($leave['emergency_contact']); ?>
										<?php if($leave['emergency_phone']) { ?>
											- <?php echo htmlspecialchars($leave['emergency_phone']); ?>
										<?php } ?>
									</div>
									<?php } ?>
									
									<?php if($leave['attachment']) { ?>
									<div style="margin: 10px 0;">
										<strong>Attachment:</strong><br>
										<a href="upload/leave_attachments/<?php echo $leave['attachment']; ?>" 
										   target="_blank" class="attachment-link">
											<i class="fa fa-paperclip"></i> View Document
										</a>
									</div>
									<?php } ?>
									
									<div style="margin: 10px 0; font-size: 12px; color: #6c757d;">
										<strong>Applied:</strong> <?php echo date('M d, Y g:i A', strtotime($leave['applied_date'])); ?>
									</div>
									
									<?php if($leave['status'] != 'pending') { ?>
									<div style="margin: 10px 0;">
										<?php if($leave['approved_date']) { ?>
										<div style="font-size: 12px; color: #6c757d;">
											<strong><?php echo ucfirst($leave['status']); ?> on:</strong> 
											<?php echo date('M d, Y g:i A', strtotime($leave['approved_date'])); ?>
											<?php if($leave['approver_first_name']) { ?>
												by <?php echo $leave['approver_first_name'] . ' ' . $leave['approver_last_name']; ?>
											<?php } ?>
										</div>
										<?php } ?>
										
										<?php if($leave['admin_comments']) { ?>
										<div style="margin-top: 10px;">
											<strong>Admin Comments:</strong><br>
											<div style="background: #e9ecef; padding: 8px; border-radius: 4px; font-size: 14px;">
												<?php echo nl2br(htmlspecialchars($leave['admin_comments'])); ?>
											</div>
										</div>
										<?php } ?>
									</div>
									<?php } ?>
									
									<?php if($leave['status'] == 'pending') { ?>
									<div style="margin-top: 15px;">
										<button class="btn btn-sm btn-warning" onclick="cancelLeave(<?php echo $leave['leave_id']; ?>)">
											<i class="fa fa-ban"></i> Cancel Application
										</button>
									</div>
									<?php } ?>
								</div>
							</div>
							<?php } ?>
						</div>
					<?php } ?>
				</div>
			</div>
		</div>
	</div>
	
	<script>
		function cancelLeave(leaveId) {
			if(confirm('Are you sure you want to cancel this leave application?')) {
				// You can implement cancel functionality here
				// For now, just show a message
				alert('Cancel functionality will be implemented in the next update.');
			}
		}
	</script>
</body>
</html>
