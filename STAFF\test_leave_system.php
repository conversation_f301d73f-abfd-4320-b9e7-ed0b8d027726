<?php
/**
 * Test script for Leave Management System
 * This script tests database connections, table structures, and basic functionality
 */

// Include the User class
include('class/User.php');

// Start output buffering for clean display
ob_start();

echo "<h2>Leave Management System Test Results</h2>";
echo "<hr>";

// Test 1: Database Connection
echo "<h3>1. Database Connection Test</h3>";
try {
    $user = new User();
    echo "<span style='color: green;'>✓ Database connection successful</span><br>";
} catch (Exception $e) {
    echo "<span style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</span><br>";
    exit();
}

// Test 2: Table Structure Verification
echo "<h3>2. Table Structure Verification</h3>";

// Check if leave_type table exists
$query = "SHOW TABLES LIKE 'leave_type'";
$result = mysqli_query($user->dbConnect, $query);
if (mysqli_num_rows($result) > 0) {
    echo "<span style='color: green;'>✓ leave_type table exists</span><br>";
    
    // Check leave_type data
    $query = "SELECT COUNT(*) as count FROM leave_type WHERE is_active = 1";
    $result = mysqli_query($user->dbConnect, $query);
    $row = mysqli_fetch_assoc($result);
    echo "<span style='color: blue;'>→ Found " . $row['count'] . " active leave types</span><br>";
} else {
    echo "<span style='color: red;'>✗ leave_type table does not exist</span><br>";
}

// Check if staff_leave table exists
$query = "SHOW TABLES LIKE 'staff_leave'";
$result = mysqli_query($user->dbConnect, $query);
if (mysqli_num_rows($result) > 0) {
    echo "<span style='color: green;'>✓ staff_leave table exists</span><br>";
    
    // Check staff_leave data
    $query = "SELECT COUNT(*) as count FROM staff_leave";
    $result = mysqli_query($user->dbConnect, $query);
    $row = mysqli_fetch_assoc($result);
    echo "<span style='color: blue;'>→ Found " . $row['count'] . " leave applications</span><br>";
} else {
    echo "<span style='color: red;'>✗ staff_leave table does not exist</span><br>";
}

// Test 3: User Class Methods
echo "<h3>3. User Class Methods Test</h3>";

// Test getLeaveTypes method
try {
    $leaveTypes = $user->getLeaveTypes();
    if (!empty($leaveTypes)) {
        echo "<span style='color: green;'>✓ getLeaveTypes() method working</span><br>";
        echo "<span style='color: blue;'>→ Available leave types:</span><br>";
        foreach ($leaveTypes as $type) {
            echo "<span style='color: blue;'>  - " . $type['leave_type_name'] . " (Max: " . $type['max_days_per_year'] . " days)</span><br>";
        }
    } else {
        echo "<span style='color: orange;'>⚠ getLeaveTypes() returned empty result</span><br>";
    }
} catch (Exception $e) {
    echo "<span style='color: red;'>✗ getLeaveTypes() method failed: " . $e->getMessage() . "</span><br>";
}

// Test 4: Session and Authentication
echo "<h3>4. Session and Authentication Test</h3>";
session_start();

if (isset($_SESSION['userid'])) {
    echo "<span style='color: green;'>✓ User session active (User ID: " . $_SESSION['userid'] . ")</span><br>";
    
    // Test getLeaveStats method
    try {
        $leaveStats = $user->getLeaveStats();
        if (!empty($leaveStats)) {
            echo "<span style='color: green;'>✓ getLeaveStats() method working</span><br>";
            echo "<span style='color: blue;'>→ Leave statistics for current user:</span><br>";
            foreach ($leaveStats as $stat) {
                echo "<span style='color: blue;'>  - " . $stat['leave_type_name'] . ": Used " . $stat['used_days'] . "/" . $stat['max_days_per_year'] . " days</span><br>";
            }
        } else {
            echo "<span style='color: orange;'>⚠ getLeaveStats() returned empty result</span><br>";
        }
    } catch (Exception $e) {
        echo "<span style='color: red;'>✗ getLeaveStats() method failed: " . $e->getMessage() . "</span><br>";
    }
    
    // Test getStaffLeaves method
    try {
        $staffLeaves = $user->getStaffLeaves();
        echo "<span style='color: green;'>✓ getStaffLeaves() method working</span><br>";
        echo "<span style='color: blue;'>→ Found " . count($staffLeaves) . " leave applications for current user</span><br>";
    } catch (Exception $e) {
        echo "<span style='color: red;'>✗ getStaffLeaves() method failed: " . $e->getMessage() . "</span><br>";
    }
} else {
    echo "<span style='color: orange;'>⚠ No active user session (testing with anonymous access)</span><br>";
}

// Test 5: File Structure
echo "<h3>5. File Structure Test</h3>";

$requiredFiles = [
    'apply_leave.php',
    'my_leaves.php',
    'leave_dashboard.php',
    'CSS/leave-management.css',
    'JS/leave-management.js'
];

foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        echo "<span style='color: green;'>✓ " . $file . " exists</span><br>";
    } else {
        echo "<span style='color: red;'>✗ " . $file . " missing</span><br>";
    }
}

// Test 6: Configuration
echo "<h3>6. Configuration Test</h3>";

// Check config.php
if (file_exists('../config.php')) {
    echo "<span style='color: green;'>✓ config.php exists</span><br>";
    
    // Check if new table configurations are present
    include('../config.php');
    $config = new dbConfig();
    
    if (property_exists($config, 'staff_leaveTable')) {
        echo "<span style='color: green;'>✓ staff_leaveTable configuration found</span><br>";
    } else {
        echo "<span style='color: red;'>✗ staff_leaveTable configuration missing</span><br>";
    }
    
    if (property_exists($config, 'leave_typeTable')) {
        echo "<span style='color: green;'>✓ leave_typeTable configuration found</span><br>";
    } else {
        echo "<span style='color: red;'>✗ leave_typeTable configuration missing</span><br>";
    }
} else {
    echo "<span style='color: red;'>✗ config.php not found</span><br>";
}

// Test 7: Upload Directory
echo "<h3>7. Upload Directory Test</h3>";

$uploadDir = 'upload/leave_attachments/';
if (!file_exists($uploadDir)) {
    if (mkdir($uploadDir, 0777, true)) {
        echo "<span style='color: green;'>✓ Created upload directory: " . $uploadDir . "</span><br>";
    } else {
        echo "<span style='color: red;'>✗ Failed to create upload directory: " . $uploadDir . "</span><br>";
    }
} else {
    echo "<span style='color: green;'>✓ Upload directory exists: " . $uploadDir . "</span><br>";
}

// Check directory permissions
if (is_writable($uploadDir)) {
    echo "<span style='color: green;'>✓ Upload directory is writable</span><br>";
} else {
    echo "<span style='color: red;'>✗ Upload directory is not writable</span><br>";
}

// Test 8: Menu Integration
echo "<h3>8. Menu Integration Test</h3>";

if (file_exists('menu.php')) {
    $menuContent = file_get_contents('menu.php');
    if (strpos($menuContent, 'leave_dashboard.php') !== false) {
        echo "<span style='color: green;'>✓ Leave menu items found in menu.php</span><br>";
    } else {
        echo "<span style='color: orange;'>⚠ Leave menu items not found in menu.php</span><br>";
    }
} else {
    echo "<span style='color: red;'>✗ menu.php not found</span><br>";
}

// Test Summary
echo "<h3>Test Summary</h3>";
echo "<hr>";

$content = ob_get_contents();
$successCount = substr_count($content, '✓');
$warningCount = substr_count($content, '⚠');
$errorCount = substr_count($content, '✗');

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
echo "<strong>Test Results:</strong><br>";
echo "<span style='color: green;'>✓ Passed: " . $successCount . "</span><br>";
echo "<span style='color: orange;'>⚠ Warnings: " . $warningCount . "</span><br>";
echo "<span style='color: red;'>✗ Failed: " . $errorCount . "</span><br>";

if ($errorCount == 0) {
    echo "<br><span style='color: green; font-weight: bold;'>🎉 All critical tests passed! The Leave Management System is ready for use.</span>";
} else {
    echo "<br><span style='color: red; font-weight: bold;'>❌ Some tests failed. Please review and fix the issues above.</span>";
}
echo "</div>";

// Installation Instructions
echo "<h3>Installation Instructions</h3>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff;'>";
echo "<strong>To complete the installation:</strong><br>";
echo "1. Run the SQL script: <code>SCHOOL/SQL/staff_leave_tables.sql</code><br>";
echo "2. Ensure upload directory permissions are correct<br>";
echo "3. Test the functionality by logging in as a staff member<br>";
echo "4. Test admin approval workflow in the SCHOOL module<br>";
echo "5. Configure email notifications if needed<br>";
echo "</div>";

ob_end_flush();
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background: #f8f9fa;
}
h2, h3 {
    color: #495057;
}
hr {
    border: 1px solid #dee2e6;
}
code {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: monospace;
}
</style>
