/* Leave Management Styles */

/* General Leave Styles */
.leave-container {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 20px 0;
}

.leave-card {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
}

.leave-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.leave-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

/* Status Styles */
.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-approved {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-rejected {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-cancelled {
    background: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

/* Form Styles */
.leave-form {
    background: #fff;
    padding: 25px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    margin-bottom: 20px;
}

.leave-form .form-group {
    margin-bottom: 20px;
}

.leave-form label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.leave-form .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 10px 15px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.leave-form .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.leave-form .btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

/* Stats Cards */
.stats-card {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    text-align: center;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.stats-header {
    font-weight: bold;
    color: #495057;
    margin-bottom: 15px;
    font-size: 16px;
}

.leave-balance {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 5px 0;
    border-bottom: 1px solid #f8f9fa;
}

.leave-balance:last-child {
    border-bottom: none;
}

.balance-used {
    color: #dc3545;
    font-weight: 600;
}

.balance-pending {
    color: #ffc107;
    font-weight: 600;
}

.balance-remaining {
    color: #28a745;
    font-weight: 600;
}

/* Dashboard Styles */
.dashboard-card {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-header {
    font-size: 18px;
    font-weight: bold;
    color: #495057;
    margin-bottom: 15px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

.quick-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.action-btn {
    flex: 1;
    min-width: 150px;
    padding: 12px 20px;
    border-radius: 6px;
    text-decoration: none;
    text-align: center;
    font-weight: 500;
    transition: all 0.3s ease;
}

.action-btn:hover {
    transform: translateY(-1px);
    text-decoration: none;
}

/* Welcome Section */
.welcome-section {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 30px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: center;
}

.welcome-section h2 {
    margin-bottom: 10px;
    font-weight: 300;
}

/* Recent Applications */
.recent-leave {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #dee2e6;
    transition: background-color 0.3s ease;
}

.recent-leave:hover {
    background-color: #f8f9fa;
}

.recent-leave:last-child {
    border-bottom: none;
}

.leave-info {
    flex: 1;
}

.leave-info strong {
    color: #495057;
}

.leave-info small {
    color: #6c757d;
}

/* Attachment Styles */
.attachment-link {
    color: #007bff;
    text-decoration: none;
    padding: 8px 12px;
    border: 1px solid #007bff;
    border-radius: 4px;
    display: inline-block;
    transition: all 0.3s ease;
}

.attachment-link:hover {
    background-color: #007bff;
    color: white;
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .leave-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .quick-actions {
        flex-direction: column;
    }
    
    .action-btn {
        min-width: 100%;
    }
    
    .leave-balance {
        font-size: 14px;
    }
    
    .stats-card {
        margin-bottom: 10px;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #007bff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Print Styles */
@media print {
    .leave-card {
        box-shadow: none;
        border: 1px solid #000;
        break-inside: avoid;
    }
    
    .btn, .action-btn {
        display: none;
    }
    
    .welcome-section {
        background: #f8f9fa !important;
        color: #000 !important;
    }
}
