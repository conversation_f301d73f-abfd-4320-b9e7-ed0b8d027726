<?php 
include('class/User.php');
$user = new User();
$user->loginStatus();

$message = '';
if(!empty($_POST["apply_leave"])) {
	$message = $user->applyLeave();
}

$leaveTypes = $user->getLeaveTypes();
$leaveStats = $user->getLeaveStats();
?>
<!DOCTYPE html>
<html>
<head>
	<title>Apply for Leave - Staff Portal</title>
	<?php include('include/header.php'); ?>
	<link rel="stylesheet" href="css/dataTables.bootstrap4.min.css" />
	<style>
		.leave-form {
			background: #f8f9fa;
			padding: 20px;
			border-radius: 8px;
			margin-bottom: 20px;
		}
		.stats-card {
			background: #fff;
			border: 1px solid #dee2e6;
			border-radius: 8px;
			padding: 15px;
			margin-bottom: 15px;
		}
		.stats-header {
			font-weight: bold;
			color: #495057;
			margin-bottom: 10px;
		}
		.leave-balance {
			display: flex;
			justify-content: space-between;
			margin-bottom: 5px;
		}
		.balance-used { color: #dc3545; }
		.balance-pending { color: #ffc107; }
		.balance-remaining { color: #28a745; }
	</style>
</head>
<body>
	<?php include('include/container.php');?>
	<div class="container contact">
		<h2>Staff Leave Management System - Apply for Leave</h2>

		<?php include('staffmenu.php'); ?>
		<div class="col-lg-10 col-md-10 col-sm-9 col-xs-12">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h3><i class="fa fa-calendar"></i> Apply for Leave</h3>
				</div>
				<div class="panel-body">
					<?php if($message != '') { ?>
						<div class="alert alert-info">
							<?php echo $message; ?>
						</div>
					<?php } ?>
					
					<!-- Leave Balance Summary -->
					<div class="row">
						<div class="col-md-12">
							<h4>Your Leave Balance (<?php echo date('Y'); ?>)</h4>
							<div class="row">
								<?php foreach($leaveStats as $stat) { ?>
								<div class="col-md-3">
									<div class="stats-card">
										<div class="stats-header"><?php echo $stat['leave_type_name']; ?></div>
										<div class="leave-balance">
											<span>Total:</span>
											<span><?php echo $stat['max_days_per_year']; ?> days</span>
										</div>
										<div class="leave-balance">
											<span>Used:</span>
											<span class="balance-used"><?php echo $stat['used_days']; ?> days</span>
										</div>
										<div class="leave-balance">
											<span>Pending:</span>
											<span class="balance-pending"><?php echo $stat['pending_days']; ?> days</span>
										</div>
										<div class="leave-balance">
											<span>Remaining:</span>
											<span class="balance-remaining"><?php echo $stat['remaining_days']; ?> days</span>
										</div>
									</div>
								</div>
								<?php } ?>
							</div>
						</div>
					</div>
					
					<!-- Leave Application Form -->
					<div class="row">
						<div class="col-md-8">
							<div class="leave-form">
								<h4>Submit Leave Application</h4>
								<form method="post" enctype="multipart/form-data">
									<div class="form-group">
										<label for="leave_type_id">Leave Type <span class="text-danger">*</span></label>
										<select class="form-control" id="leave_type_id" name="leave_type_id" required>
											<option value="">Select Leave Type</option>
											<?php foreach($leaveTypes as $type) { ?>
											<option value="<?php echo $type['leave_type_id']; ?>">
												<?php echo $type['leave_type_name']; ?> (Max: <?php echo $type['max_days_per_year']; ?> days/year)
											</option>
											<?php } ?>
										</select>
									</div>
									
									<div class="row">
										<div class="col-md-6">
											<div class="form-group">
												<label for="start_date">Start Date <span class="text-danger">*</span></label>
												<input type="date" class="form-control" id="start_date" name="start_date" required>
											</div>
										</div>
										<div class="col-md-6">
											<div class="form-group">
												<label for="end_date">End Date <span class="text-danger">*</span></label>
												<input type="date" class="form-control" id="end_date" name="end_date" required>
											</div>
										</div>
									</div>
									
									<div class="form-group">
										<label for="reason">Reason for Leave <span class="text-danger">*</span></label>
										<textarea class="form-control" id="reason" name="reason" rows="4" required placeholder="Please provide detailed reason for your leave application"></textarea>
									</div>
									
									<div class="row">
										<div class="col-md-6">
											<div class="form-group">
												<label for="emergency_contact">Emergency Contact Name</label>
												<input type="text" class="form-control" id="emergency_contact" name="emergency_contact" placeholder="Contact person during leave">
											</div>
										</div>
										<div class="col-md-6">
											<div class="form-group">
												<label for="emergency_phone">Emergency Contact Phone</label>
												<input type="text" class="form-control" id="emergency_phone" name="emergency_phone" placeholder="Contact phone number">
											</div>
										</div>
									</div>
									
									<div class="form-group">
										<label for="attachment">Supporting Document (Optional)</label>
										<input type="file" class="form-control" id="attachment" name="attachment" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
										<small class="form-text text-muted">Upload medical certificate or other supporting documents if applicable.</small>
									</div>
									
									<div class="form-group">
										<button type="submit" name="apply_leave" class="btn btn-primary">
											<i class="fa fa-paper-plane"></i> Submit Application
										</button>
										<a href="my_leaves.php" class="btn btn-secondary">
											<i class="fa fa-list"></i> View My Applications
										</a>
									</div>
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	
	<script>
		// Calculate total days when dates change
		document.getElementById('start_date').addEventListener('change', calculateDays);
		document.getElementById('end_date').addEventListener('change', calculateDays);
		
		function calculateDays() {
			var startDate = document.getElementById('start_date').value;
			var endDate = document.getElementById('end_date').value;
			
			if(startDate && endDate) {
				var start = new Date(startDate);
				var end = new Date(endDate);
				var timeDiff = end.getTime() - start.getTime();
				var daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
				
				if(daysDiff > 0) {
					// You can add a display element to show calculated days
					console.log('Total days: ' + daysDiff);
				}
			}
		}
		
		// Set minimum date to today
		document.getElementById('start_date').min = new Date().toISOString().split('T')[0];
		document.getElementById('end_date').min = new Date().toISOString().split('T')[0];
	</script>
</body>
</html>
