/* Leave Administration Styles for School Module */

/* Admin Dashboard Styles */
.admin-leave-container {
    background: #f8f9fa;
    min-height: 100vh;
}

.leave-admin-card {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.leave-admin-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

/* Filter Tabs */
.filter-tabs .nav-tabs {
    border-bottom: 2px solid #007bff;
    margin-bottom: 20px;
}

.filter-tabs .nav-link {
    color: #495057;
    border: none;
    border-radius: 0;
    padding: 12px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.filter-tabs .nav-link:hover {
    border-color: transparent;
    background-color: #f8f9fa;
}

.filter-tabs .nav-link.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-approved {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-rejected {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-cancelled {
    background: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

/* Application Cards */
.application-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    background: #fff;
    transition: all 0.3s ease;
}

.application-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 8px rgba(0,123,255,0.1);
}

.application-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.staff-info {
    font-weight: bold;
    color: #495057;
    font-size: 16px;
}

.staff-email {
    font-size: 14px;
    color: #6c757d;
    margin-top: 2px;
}

.leave-dates {
    color: #6c757d;
    font-size: 14px;
    margin: 10px 0;
}

.leave-reason {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    margin: 10px 0;
    border-left: 4px solid #007bff;
}

/* Action Buttons */
.action-buttons {
    margin-top: 15px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-approve {
    background: #28a745;
    border-color: #28a745;
    color: white;
}

.btn-approve:hover {
    background: #218838;
    border-color: #1e7e34;
    color: white;
}

.btn-reject {
    background: #dc3545;
    border-color: #dc3545;
    color: white;
}

.btn-reject:hover {
    background: #c82333;
    border-color: #bd2130;
    color: white;
}

/* Detail View Styles */
.detail-section {
    margin-bottom: 30px;
}

.detail-section h4 {
    color: #495057;
    border-bottom: 2px solid #007bff;
    padding-bottom: 8px;
    margin-bottom: 15px;
}

.info-grid {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 10px;
    margin-bottom: 10px;
}

.info-label {
    font-weight: 600;
    color: #495057;
}

.info-value {
    color: #6c757d;
}

/* Summary Cards */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 25px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0,123,255,0.2);
}

.summary-number {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 5px;
}

.summary-label {
    font-size: 14px;
    opacity: 0.9;
}

/* Reports Styles */
.report-card {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.staff-report-header {
    font-weight: bold;
    color: #495057;
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.leave-type-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 8px;
    background: #f8f9fa;
    border-radius: 6px;
}

.leave-type-name {
    font-weight: 500;
    color: #495057;
}

.leave-numbers {
    font-size: 14px;
}

.used-days { color: #dc3545; font-weight: 600; }
.pending-days { color: #ffc107; font-weight: 600; }
.remaining-days { color: #28a745; font-weight: 600; }

/* Modal Styles */
.modal-header {
    background: #007bff;
    color: white;
    border-radius: 8px 8px 0 0;
}

.modal-header .close {
    color: white;
    opacity: 0.8;
}

.modal-header .close:hover {
    opacity: 1;
}

/* Year Selector */
.year-selector {
    background: #fff;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    margin-bottom: 20px;
}

.year-selector .form-control {
    display: inline-block;
    width: auto;
    margin-left: 10px;
}

/* Export Buttons */
.export-buttons {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.export-buttons .btn {
    margin-right: 10px;
    margin-bottom: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .application-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .action-buttons .btn {
        width: 100%;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
        gap: 5px;
    }
    
    .info-label {
        font-weight: bold;
    }
    
    .summary-cards {
        grid-template-columns: 1fr;
    }
    
    .leave-type-summary {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

/* Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Print Styles */
@media print {
    .action-buttons,
    .export-buttons,
    .filter-tabs,
    .btn {
        display: none !important;
    }
    
    .application-card,
    .report-card {
        border: 1px solid #000;
        box-shadow: none;
        break-inside: avoid;
        margin-bottom: 20px;
    }
    
    .summary-card {
        background: #f8f9fa !important;
        color: #000 !important;
        border: 1px solid #000;
    }
}

/* Animation Classes */
.fade-in-up {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
