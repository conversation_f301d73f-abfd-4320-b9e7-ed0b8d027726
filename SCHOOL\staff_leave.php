<?php 
include('class/School.php');
$user = new School();
$user->adminLoginStatus();

$message = '';
if(!empty($_POST["process_leave"])) {
	$message = $user->processLeaveApplication();
}

// Get filter status
$filterStatus = isset($_GET['status']) ? $_GET['status'] : null;
$leaveApplications = $user->getAllLeaveApplications($filterStatus);
?>
<!DOCTYPE html>
<html>
<head>
	<title>Staff Leave Management - School Admin</title>
	<?php include('inc/header.php'); ?>
	<?php include('include_files.php');?>
	<script src="js/jquery.dataTables.min.js"></script>
	<script src="js/dataTables.bootstrap.min.js"></script>		
	<link rel="stylesheet" href="css/dataTables.bootstrap.min.css" />
	<style>
		.status-pending { color: #ffc107; font-weight: bold; }
		.status-approved { color: #28a745; font-weight: bold; }
		.status-rejected { color: #dc3545; font-weight: bold; }
		.status-cancelled { color: #6c757d; font-weight: bold; }
		.leave-card {
			border: 1px solid #dee2e6;
			border-radius: 8px;
			padding: 15px;
			margin-bottom: 15px;
			background: #fff;
		}
		.leave-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 10px;
			border-bottom: 1px solid #eee;
			padding-bottom: 10px;
		}
		.staff-info {
			font-weight: bold;
			color: #495057;
		}
		.leave-dates {
			color: #6c757d;
			font-size: 14px;
		}
		.filter-tabs {
			margin-bottom: 20px;
		}
		.filter-tabs .nav-link {
			color: #495057;
		}
		.filter-tabs .nav-link.active {
			background-color: #007bff;
			color: white;
		}
	</style>
</head>
<body>
	<?php include('inc/container.php');?>
	<div class="container">	
		<?php include('side-menu.php');	?>
		<div class="content">
			<div class="container-fluid">
				<div>   
					<a href="#"><strong><span class="ti-calendar"></span> Staff Leave Management</strong></a>
					<hr>		
					<div class="panel-heading">
						<div class="row">
							<div class="col-md-10">
								<h3 class="panel-title"></h3>
							</div>
						</div>
					</div>
					
					<?php if($message != '') { ?>
						<div class="alert alert-info alert-dismissible">
							<button type="button" class="close" data-dismiss="alert">&times;</button>
							<?php echo $message; ?>
						</div>
					<?php } ?>
					
					<!-- Filter Tabs -->
					<div class="filter-tabs">
						<ul class="nav nav-tabs">
							<li class="nav-item">
								<a class="nav-link <?php echo $filterStatus === null ? 'active' : ''; ?>" 
								   href="staff_leave.php">All Applications</a>
							</li>
							<li class="nav-item">
								<a class="nav-link <?php echo $filterStatus === 'pending' ? 'active' : ''; ?>" 
								   href="staff_leave.php?status=pending">Pending</a>
							</li>
							<li class="nav-item">
								<a class="nav-link <?php echo $filterStatus === 'approved' ? 'active' : ''; ?>" 
								   href="staff_leave.php?status=approved">Approved</a>
							</li>
							<li class="nav-item">
								<a class="nav-link <?php echo $filterStatus === 'rejected' ? 'active' : ''; ?>" 
								   href="staff_leave.php?status=rejected">Rejected</a>
							</li>
						</ul>
					</div>
					
					<div class="panel panel-default">
						<div class="panel-body">
							<?php if(empty($leaveApplications)) { ?>
								<div class="alert alert-info">
									<i class="ti-info"></i> No leave applications found.
								</div>
							<?php } else { ?>
								<div class="row">
									<?php foreach($leaveApplications as $leave) { 
										$statusClass = 'status-' . $leave['status'];
										$statusIcon = '';
										switch($leave['status']) {
											case 'pending': $statusIcon = 'ti-time'; break;
											case 'approved': $statusIcon = 'ti-check'; break;
											case 'rejected': $statusIcon = 'ti-close'; break;
											case 'cancelled': $statusIcon = 'ti-na'; break;
										}
									?>
									<div class="col-md-6">
										<div class="leave-card">
											<div class="leave-header">
												<div>
													<div class="staff-info">
														<?php echo $leave['staff_first_name'] . ' ' . $leave['staff_last_name']; ?>
													</div>
													<div style="font-size: 14px; color: #6c757d;">
														<?php echo $leave['staff_email']; ?>
													</div>
												</div>
												<div>
													<span class="<?php echo $statusClass; ?>">
														<i class="<?php echo $statusIcon; ?>"></i>
														<?php echo ucfirst($leave['status']); ?>
													</span>
												</div>
											</div>
											
											<div style="margin-bottom: 10px;">
												<strong><?php echo $leave['leave_type_name']; ?></strong>
											</div>
											
											<div class="leave-dates">
												<i class="ti-calendar"></i>
												<?php echo date('M d, Y', strtotime($leave['start_date'])); ?> - 
												<?php echo date('M d, Y', strtotime($leave['end_date'])); ?>
												(<?php echo $leave['total_days']; ?> day<?php echo $leave['total_days'] > 1 ? 's' : ''; ?>)
											</div>
											
											<div style="margin: 10px 0;">
												<strong>Reason:</strong><br>
												<?php echo nl2br(htmlspecialchars(substr($leave['reason'], 0, 150))); ?>
												<?php if(strlen($leave['reason']) > 150) echo '...'; ?>
											</div>
											
											<div style="margin: 10px 0; font-size: 12px; color: #6c757d;">
												<strong>Applied:</strong> <?php echo date('M d, Y g:i A', strtotime($leave['applied_date'])); ?>
											</div>
											
											<?php if($leave['status'] != 'pending') { ?>
											<div style="margin: 10px 0; font-size: 12px; color: #6c757d;">
												<?php if($leave['approved_date']) { ?>
													<strong><?php echo ucfirst($leave['status']); ?> on:</strong> 
													<?php echo date('M d, Y g:i A', strtotime($leave['approved_date'])); ?>
													<?php if($leave['approver_first_name']) { ?>
														by <?php echo $leave['approver_first_name'] . ' ' . $leave['approver_last_name']; ?>
													<?php } ?>
												<?php } ?>
											</div>
											<?php } ?>
											
											<div style="margin-top: 15px;">
												<a href="leave_details.php?id=<?php echo $leave['leave_id']; ?>" 
												   class="btn btn-info btn-sm">
													<i class="ti-eye"></i> View Details
												</a>
												
												<?php if($leave['status'] == 'pending') { ?>
												<button class="btn btn-success btn-sm" 
														onclick="processLeave(<?php echo $leave['leave_id']; ?>, 'approved')">
													<i class="ti-check"></i> Approve
												</button>
												<button class="btn btn-danger btn-sm" 
														onclick="processLeave(<?php echo $leave['leave_id']; ?>, 'rejected')">
													<i class="ti-close"></i> Reject
												</button>
												<?php } ?>
											</div>
										</div>
									</div>
									<?php } ?>
								</div>
							<?php } ?>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	
	<!-- Process Leave Modal -->
	<div class="modal fade" id="processLeaveModal" tabindex="-1" role="dialog">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<form method="post">
					<div class="modal-header">
						<h4 class="modal-title" id="modalTitle">Process Leave Application</h4>
						<button type="button" class="close" data-dismiss="modal">&times;</button>
					</div>
					<div class="modal-body">
						<input type="hidden" id="leave_id" name="leave_id">
						<input type="hidden" id="action" name="action">
						
						<div class="form-group">
							<label for="admin_comments">Comments (Optional):</label>
							<textarea class="form-control" id="admin_comments" name="admin_comments" 
									  rows="4" placeholder="Add any comments or notes..."></textarea>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
						<button type="submit" name="process_leave" class="btn btn-primary" id="confirmBtn">Confirm</button>
					</div>
				</form>
			</div>
		</div>
	</div>
	
	<script>
		function processLeave(leaveId, action) {
			document.getElementById('leave_id').value = leaveId;
			document.getElementById('action').value = action;
			
			var title = action === 'approved' ? 'Approve Leave Application' : 'Reject Leave Application';
			var btnText = action === 'approved' ? 'Approve' : 'Reject';
			var btnClass = action === 'approved' ? 'btn-success' : 'btn-danger';
			
			document.getElementById('modalTitle').textContent = title;
			document.getElementById('confirmBtn').textContent = btnText;
			document.getElementById('confirmBtn').className = 'btn ' + btnClass;
			
			$('#processLeaveModal').modal('show');
		}
	</script>
</body>
</html>
