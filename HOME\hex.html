<html xmlns:v="urn:schemas-microsoft-com:vml"
xmlns:o="urn:schemas-microsoft-com:office:office"
xmlns:w="urn:schemas-microsoft-com:office:word"
xmlns:m="http://schemas.microsoft.com/office/2004/12/omml"
xmlns="http://www.w3.org/TR/REC-html40">

<head>
<meta http-equiv=Content-Type content="text/html; charset=windows-1252">
<meta name=ProgId content=Word.Document>
<meta name=Generator content="Microsoft Word 15">
<meta name=Originator content="Microsoft Word 15">
<link rel=File-List href="hex_files/filelist.xml">
<link rel=Edit-Time-Data href="hex_files/editdata.mso">
<!--[if !mso]>
<style>
v\:* {behavior:url(#default#VML);}
o\:* {behavior:url(#default#VML);}
w\:* {behavior:url(#default#VML);}
.shape {behavior:url(#default#VML);}
</style>
<![endif]--><!--[if gte mso 9]><xml>
 <o:DocumentProperties>
  <o:Author>Richard</o:Author>
  <o:LastAuthor>Richard</o:LastAuthor>
  <o:Revision>2</o:Revision>
  <o:TotalTime>4</o:TotalTime>
  <o:Created>2020-06-20T23:22:00Z</o:Created>
  <o:LastSaved>2020-06-20T23:22:00Z</o:LastSaved>
  <o:Pages>1</o:Pages>
  <o:Characters>2</o:Characters>
  <o:Lines>1</o:Lines>
  <o:Paragraphs>1</o:Paragraphs>
  <o:CharactersWithSpaces>2</o:CharactersWithSpaces>
  <o:Version>16.00</o:Version>
 </o:DocumentProperties>
 <o:OfficeDocumentSettings>
  <o:AllowPNG/>
 </o:OfficeDocumentSettings>
</xml><![endif]-->
<link rel=themeData href="hex_files/themedata.thmx">
<link rel=colorSchemeMapping href="hex_files/colorschememapping.xml">
<!--[if gte mso 9]><xml>
 <w:WordDocument>
  <w:SpellingState>Clean</w:SpellingState>
  <w:GrammarState>Clean</w:GrammarState>
  <w:TrackMoves>false</w:TrackMoves>
  <w:TrackFormatting/>
  <w:PunctuationKerning/>
  <w:ValidateAgainstSchemas/>
  <w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid>
  <w:IgnoreMixedContent>false</w:IgnoreMixedContent>
  <w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText>
  <w:DoNotPromoteQF/>
  <w:LidThemeOther>EN-US</w:LidThemeOther>
  <w:LidThemeAsian>ZH-CN</w:LidThemeAsian>
  <w:LidThemeComplexScript>X-NONE</w:LidThemeComplexScript>
  <w:Compatibility>
   <w:BreakWrappedTables/>
   <w:SnapToGridInCell/>
   <w:WrapTextWithPunct/>
   <w:UseAsianBreakRules/>
   <w:DontGrowAutofit/>
   <w:SplitPgBreakAndParaMark/>
   <w:EnableOpenTypeKerning/>
   <w:DontFlipMirrorIndents/>
   <w:OverrideTableStyleHps/>
   <w:UseFELayout/>
  </w:Compatibility>
  <m:mathPr>
   <m:mathFont m:val="Cambria Math"/>
   <m:brkBin m:val="before"/>
   <m:brkBinSub m:val="&#45;-"/>
   <m:smallFrac m:val="off"/>
   <m:dispDef/>
   <m:lMargin m:val="0"/>
   <m:rMargin m:val="0"/>
   <m:defJc m:val="centerGroup"/>
   <m:wrapIndent m:val="1440"/>
   <m:intLim m:val="subSup"/>
   <m:naryLim m:val="undOvr"/>
  </m:mathPr></w:WordDocument>
</xml><![endif]--><!--[if gte mso 9]><xml>
 <w:LatentStyles DefLockedState="false" DefUnhideWhenUsed="false"
  DefSemiHidden="false" DefQFormat="false" DefPriority="99"
  LatentStyleCount="376">
  <w:LsdException Locked="false" Priority="0" QFormat="true" Name="Normal"/>
  <w:LsdException Locked="false" Priority="9" QFormat="true" Name="heading 1"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 2"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 3"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 4"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 5"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 6"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 7"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 8"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 9"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 6"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 7"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 8"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 9"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 1"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 2"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 3"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 4"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 5"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 6"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 7"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 8"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 9"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Normal Indent"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="footnote text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="annotation text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="header"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="footer"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index heading"/>
  <w:LsdException Locked="false" Priority="35" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="caption"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="table of figures"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="envelope address"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="envelope return"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="footnote reference"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="annotation reference"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="line number"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="page number"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="endnote reference"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="endnote text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="table of authorities"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="macro"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="toa heading"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Bullet"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Number"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Bullet 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Bullet 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Bullet 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Bullet 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Number 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Number 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Number 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Number 5"/>
  <w:LsdException Locked="false" Priority="10" QFormat="true" Name="Title"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Closing"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Signature"/>
  <w:LsdException Locked="false" Priority="1" SemiHidden="true"
   UnhideWhenUsed="true" Name="Default Paragraph Font"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text Indent"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Continue"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Continue 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Continue 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Continue 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Continue 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Message Header"/>
  <w:LsdException Locked="false" Priority="11" QFormat="true" Name="Subtitle"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Salutation"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Date"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text First Indent"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text First Indent 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Note Heading"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text Indent 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text Indent 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Block Text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Hyperlink"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="FollowedHyperlink"/>
  <w:LsdException Locked="false" Priority="22" QFormat="true" Name="Strong"/>
  <w:LsdException Locked="false" Priority="20" QFormat="true" Name="Emphasis"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Document Map"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Plain Text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="E-mail Signature"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Top of Form"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Bottom of Form"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Normal (Web)"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Acronym"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Address"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Cite"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Code"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Definition"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Keyboard"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Preformatted"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Sample"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Typewriter"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Variable"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Normal Table"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="annotation subject"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="No List"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Outline List 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Outline List 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Outline List 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Simple 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Simple 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Simple 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Classic 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Classic 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Classic 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Classic 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Colorful 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Colorful 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Colorful 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 6"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 7"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 8"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 6"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 7"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 8"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table 3D effects 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table 3D effects 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table 3D effects 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Contemporary"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Elegant"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Professional"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Subtle 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Subtle 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Web 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Web 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Web 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Balloon Text"/>
  <w:LsdException Locked="false" Priority="59" Name="Table Grid"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Theme"/>
  <w:LsdException Locked="false" SemiHidden="true" Name="Placeholder Text"/>
  <w:LsdException Locked="false" Priority="1" QFormat="true" Name="No Spacing"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 1"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 1"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 1"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 1"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 1"/>
  <w:LsdException Locked="false" SemiHidden="true" Name="Revision"/>
  <w:LsdException Locked="false" Priority="34" QFormat="true"
   Name="List Paragraph"/>
  <w:LsdException Locked="false" Priority="29" QFormat="true" Name="Quote"/>
  <w:LsdException Locked="false" Priority="30" QFormat="true"
   Name="Intense Quote"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 1"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 1"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 1"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 1"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 1"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 1"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 2"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 2"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 2"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 2"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 2"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 2"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 2"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 2"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 2"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 2"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 2"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 3"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 3"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 3"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 3"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 3"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 3"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 3"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 3"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 3"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 3"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 3"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 4"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 4"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 4"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 4"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 4"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 4"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 4"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 4"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 4"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 4"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 4"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 5"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 5"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 5"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 5"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 5"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 5"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 5"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 5"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 5"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 5"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 5"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 6"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 6"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 6"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 6"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 6"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 6"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 6"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 6"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 6"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 6"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 6"/>
  <w:LsdException Locked="false" Priority="19" QFormat="true"
   Name="Subtle Emphasis"/>
  <w:LsdException Locked="false" Priority="21" QFormat="true"
   Name="Intense Emphasis"/>
  <w:LsdException Locked="false" Priority="31" QFormat="true"
   Name="Subtle Reference"/>
  <w:LsdException Locked="false" Priority="32" QFormat="true"
   Name="Intense Reference"/>
  <w:LsdException Locked="false" Priority="33" QFormat="true" Name="Book Title"/>
  <w:LsdException Locked="false" Priority="37" SemiHidden="true"
   UnhideWhenUsed="true" Name="Bibliography"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="TOC Heading"/>
  <w:LsdException Locked="false" Priority="41" Name="Plain Table 1"/>
  <w:LsdException Locked="false" Priority="42" Name="Plain Table 2"/>
  <w:LsdException Locked="false" Priority="43" Name="Plain Table 3"/>
  <w:LsdException Locked="false" Priority="44" Name="Plain Table 4"/>
  <w:LsdException Locked="false" Priority="45" Name="Plain Table 5"/>
  <w:LsdException Locked="false" Priority="40" Name="Grid Table Light"/>
  <w:LsdException Locked="false" Priority="46" Name="Grid Table 1 Light"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark"/>
  <w:LsdException Locked="false" Priority="51" Name="Grid Table 6 Colorful"/>
  <w:LsdException Locked="false" Priority="52" Name="Grid Table 7 Colorful"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 1"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 1"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 1"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 1"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 2"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 2"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 2"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 2"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 3"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 3"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 3"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 3"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 4"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 4"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 4"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 4"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 5"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 5"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 5"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 5"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 6"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 6"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 6"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 6"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 6"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 6"/>
  <w:LsdException Locked="false" Priority="46" Name="List Table 1 Light"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark"/>
  <w:LsdException Locked="false" Priority="51" Name="List Table 6 Colorful"/>
  <w:LsdException Locked="false" Priority="52" Name="List Table 7 Colorful"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 1"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 1"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 1"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 1"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 2"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 2"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 2"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 2"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 3"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 3"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 3"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 3"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 4"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 4"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 4"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 4"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 5"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 5"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 5"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 5"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 6"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 6"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 6"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 6"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 6"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 6"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Mention"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Smart Hyperlink"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Hashtag"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Unresolved Mention"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Smart Link"/>
 </w:LatentStyles>
</xml><![endif]-->
<style>
<!--
 /* Font Definitions */
 @font-face
	{font-family:SimSun;
	panose-1:2 1 6 0 3 1 1 1 1 1;
	mso-font-alt:\5B8B\4F53;
	mso-font-charset:134;
	mso-generic-font-family:auto;
	mso-font-pitch:variable;
	mso-font-signature:3 680460288 22 0 262145 0;}
@font-face
	{font-family:"Cambria Math";
	panose-1:2 4 5 3 5 4 6 3 2 4;
	mso-font-charset:0;
	mso-generic-font-family:roman;
	mso-font-pitch:variable;
	mso-font-signature:3 0 0 0 1 0;}
@font-face
	{font-family:Calibri;
	panose-1:2 15 5 2 2 2 4 3 2 4;
	mso-font-charset:0;
	mso-generic-font-family:swiss;
	mso-font-pitch:variable;
	mso-font-signature:-469750017 -1073732485 9 0 511 0;}
@font-face
	{font-family:"\@SimSun";
	panose-1:2 1 6 0 3 1 1 1 1 1;
	mso-font-charset:134;
	mso-generic-font-family:auto;
	mso-font-pitch:variable;
	mso-font-signature:3 680460288 22 0 262145 0;}
 /* Style Definitions */
 p.MsoNormal, li.MsoNormal, div.MsoNormal
	{mso-style-unhide:no;
	mso-style-qformat:yes;
	mso-style-parent:"";
	margin-top:0cm;
	margin-right:0cm;
	margin-bottom:10.0pt;
	margin-left:0cm;
	line-height:115%;
	mso-pagination:widow-orphan;
	font-size:11.0pt;
	font-family:"Calibri",sans-serif;
	mso-ascii-font-family:Calibri;
	mso-ascii-theme-font:minor-latin;
	mso-fareast-font-family:SimSun;
	mso-fareast-theme-font:minor-fareast;
	mso-hansi-font-family:Calibri;
	mso-hansi-theme-font:minor-latin;
	mso-bidi-font-family:"Times New Roman";
	mso-bidi-theme-font:minor-bidi;}
p.MsoHeader, li.MsoHeader, div.MsoHeader
	{mso-style-priority:99;
	mso-style-link:"Header Char";
	margin:0cm;
	margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	tab-stops:center 234.0pt right 468.0pt;
	font-size:11.0pt;
	font-family:"Calibri",sans-serif;
	mso-ascii-font-family:Calibri;
	mso-ascii-theme-font:minor-latin;
	mso-fareast-font-family:SimSun;
	mso-fareast-theme-font:minor-fareast;
	mso-hansi-font-family:Calibri;
	mso-hansi-theme-font:minor-latin;
	mso-bidi-font-family:"Times New Roman";
	mso-bidi-theme-font:minor-bidi;}
p.MsoFooter, li.MsoFooter, div.MsoFooter
	{mso-style-priority:99;
	mso-style-link:"Footer Char";
	margin:0cm;
	margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	tab-stops:center 234.0pt right 468.0pt;
	font-size:11.0pt;
	font-family:"Calibri",sans-serif;
	mso-ascii-font-family:Calibri;
	mso-ascii-theme-font:minor-latin;
	mso-fareast-font-family:SimSun;
	mso-fareast-theme-font:minor-fareast;
	mso-hansi-font-family:Calibri;
	mso-hansi-theme-font:minor-latin;
	mso-bidi-font-family:"Times New Roman";
	mso-bidi-theme-font:minor-bidi;}
span.HeaderChar
	{mso-style-name:"Header Char";
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-locked:yes;
	mso-style-link:Header;}
span.FooterChar
	{mso-style-name:"Footer Char";
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-locked:yes;
	mso-style-link:Footer;}
.MsoChpDefault
	{mso-style-type:export-only;
	mso-default-props:yes;
	font-family:"Calibri",sans-serif;
	mso-ascii-font-family:Calibri;
	mso-ascii-theme-font:minor-latin;
	mso-fareast-font-family:SimSun;
	mso-fareast-theme-font:minor-fareast;
	mso-hansi-font-family:Calibri;
	mso-hansi-theme-font:minor-latin;
	mso-bidi-font-family:"Times New Roman";
	mso-bidi-theme-font:minor-bidi;}
.MsoPapDefault
	{mso-style-type:export-only;
	margin-bottom:10.0pt;
	line-height:115%;}
 /* Page Definitions */
 @page
	{mso-footnote-separator:url("hex_files/header.html") fs;
	mso-footnote-continuation-separator:url("hex_files/header.html") fcs;
	mso-endnote-separator:url("hex_files/header.html") es;
	mso-endnote-continuation-separator:url("hex_files/header.html") ecs;}
@page WordSection1
	{size:612.0pt 792.0pt;
	margin:72.0pt 72.0pt 72.0pt 72.0pt;
	mso-header-margin:36.0pt;
	mso-footer-margin:36.0pt;
	mso-paper-source:0;}
div.WordSection1
	{page:WordSection1;}
-->
</style>
<!--[if gte mso 10]>
<style>
 /* Style Definitions */
 table.MsoNormalTable
	{mso-style-name:"Table Normal";
	mso-tstyle-rowband-size:0;
	mso-tstyle-colband-size:0;
	mso-style-noshow:yes;
	mso-style-priority:99;
	mso-style-parent:"";
	mso-padding-alt:0cm 5.4pt 0cm 5.4pt;
	mso-para-margin-top:0cm;
	mso-para-margin-right:0cm;
	mso-para-margin-bottom:10.0pt;
	mso-para-margin-left:0cm;
	line-height:115%;
	mso-pagination:widow-orphan;
	font-size:11.0pt;
	font-family:"Calibri",sans-serif;
	mso-ascii-font-family:Calibri;
	mso-ascii-theme-font:minor-latin;
	mso-hansi-font-family:Calibri;
	mso-hansi-theme-font:minor-latin;
	mso-bidi-font-family:"Times New Roman";
	mso-bidi-theme-font:minor-bidi;}
</style>
<![endif]--><!--[if gte mso 9]><xml>
 <o:shapedefaults v:ext="edit" spidmax="1027"/>
</xml><![endif]--><!--[if gte mso 9]><xml>
 <o:shapelayout v:ext="edit">
  <o:idmap v:ext="edit" data="1"/>
 </o:shapelayout></xml><![endif]-->
</head>

<body lang=EN-US style='tab-interval:36.0pt'>

<div class=WordSection1>

<p class=MsoNormal><!--[if gte vml 1]><v:shapetype id="_x0000_t75" coordsize="21600,21600"
 o:spt="75" o:preferrelative="t" path="m@4@5l@4@11@9@11@9@5xe" filled="f"
 stroked="f">
 <v:stroke joinstyle="miter"/>
 <v:formulas>
  <v:f eqn="if lineDrawn pixelLineWidth 0"/>
  <v:f eqn="sum @0 1 0"/>
  <v:f eqn="sum 0 0 @1"/>
  <v:f eqn="prod @2 1 2"/>
  <v:f eqn="prod @3 21600 pixelWidth"/>
  <v:f eqn="prod @3 21600 pixelHeight"/>
  <v:f eqn="sum @0 0 1"/>
  <v:f eqn="prod @6 1 2"/>
  <v:f eqn="prod @7 21600 pixelWidth"/>
  <v:f eqn="sum @8 21600 0"/>
  <v:f eqn="prod @7 21600 pixelHeight"/>
  <v:f eqn="sum @10 21600 0"/>
 </v:formulas>
 <v:path o:extrusionok="f" gradientshapeok="t" o:connecttype="rect"/>
 <o:lock v:ext="edit" aspectratio="t"/>
</v:shapetype><v:shape id="Picture_x0020_2" o:spid="_x0000_s1026" type="#_x0000_t75"
 style='position:absolute;margin-left:212.25pt;margin-top:118.5pt;width:81pt;
 height:81pt;z-index:251658240;visibility:visible;mso-wrap-style:square;
 mso-width-percent:0;mso-height-percent:0;mso-wrap-distance-left:9pt;
 mso-wrap-distance-top:0;mso-wrap-distance-right:9pt;
 mso-wrap-distance-bottom:0;mso-position-horizontal:absolute;
 mso-position-horizontal-relative:text;mso-position-vertical:absolute;
 mso-position-vertical-relative:text;mso-width-percent:0;mso-height-percent:0;
 mso-width-relative:page;mso-height-relative:page'>
 <v:imagedata src="hex_files/image001.jpg" o:title=""/>
</v:shape><![endif]--><![if !vml]><span style='mso-ignore:vglayout;position:
absolute;z-index:251658240;margin-left:283px;margin-top:158px;width:108px;
height:108px'><img width=108 height=108 src="hex_files/image002.jpg" v:shapes="Picture_x0020_2"></span><![endif]><span
style='mso-no-proof:yes'><!--[if gte vml 1]><v:shape id="Diagram_x0020_1"
 o:spid="_x0000_i1025" type="#_x0000_t75" style='width:7in;height:320.25pt;
 visibility:visible' o:gfxdata="UEsDBBQABgAIAAAAIQBgJjcoXAEAAHYEAAATAAAAW0NvbnRlbnRfVHlwZXNdLnhtbLSUy07DMBRE
90j8Q+QtStx2gRBq0kVTVggQlA+4sm9Sq34E20mbv8dJU1U8Ctl06cfMHF2PPF/slYwatE4YnZJp
MiERama40GVK3tcP8R2JnAfNQRqNKWnRkUV2fTVftxW6KKi1S8nG++qeUsc2qMAlpkIdTgpjFfiw
tCWtgG2hRDqbTG4pM9qj9rHvPEg2z7GAWvpotQ/bBxKL0pFoebjYZaUEqkoKBj6Q0kbzbynxkJAE
ZX/HbUTlbgIGob8mdCfnAwbdcxiNFRyjF7D+CVTAoNw6ijOTG5b87dFBKhebohAMk9y6Va86Mp3z
5gJKC8pRDh6mIzK+jnvI44bVKgw54RZ24TWVTAbjPNiOZpDQmtpfgOKxNx7N8VELtn3zrcQLsPS+
o1GYkca6C2Ase+PRHMPDjgE5tfBnGw71+DfV7LTFZkQbT1mh8XmQvWJzdKf9r5F9AgAA//8DAFBL
AwQUAAYACAAAACEAOP0h/9YAAACUAQAACwAAAF9yZWxzLy5yZWxzpJDBasMwDIbvg72D0X1xmsMY
o04vo9Br6R7A2IpjGltGMtn69jODwTJ621G/0PeJf3/4TItakSVSNrDrelCYHfmYg4H3y/HpBZRU
m71dKKOBGwocxseH/RkXW9uRzLGIapQsBuZay6vW4mZMVjoqmNtmIk62tpGDLtZdbUA99P2z5t8M
GDdMdfIG+OQHUJdbaeY/7BQdk9BUO0dJ0zRFd4+qPX3kM66NYjlgNeBZvkPGtWvPgb7v3f3TG9iW
Oboj24Rv5LZ+HKhlP3q96XL8AgAA//8DAFBLAwQUAAYACAAAACEAoPpKnZ0PAAC/TwAAFgAAAGRy
cy9kaWFncmFtcy9kYXRhMS54bWzUXNtuG8m1fT/A+QeB7zWq+0WIJ6grYmCSMWInLwfnoU3SFhGK
VEjasRHk37O6RbaaoigXZUu250HWtMRW712r9mXtVf2HP366mp99nK7Ws+XixYj9Qkdn08V4OZkt
3r8Y/e1NIXZ0tt40i0kzXy6mL0afp+vRH3/93//5w+T91cWk2TR/Xk6m8zPcZbG+wLUXo8vN5vri
/Hw9vpxeNetfltfTBX76brm6ajb439X788mq+RfufzU/55Tq88mseb9qrkbbmzSPuMVVM1uMfu2e
6Xrz23qz+/bsqn26l5MXo39T5wWnLhLJeCEyGUusdZKwnFW21jBn5X9GZ5vP17Byshzvbrd6Pd2c
zZdvcL29z4fV4uJqNl4t18t3m1/Gy6vz5bt3s/F0+0+z2sAoxs7nzeflh835n6afmvfLxV8bGDkf
4T6x2bS3GX8ez6ejs3+uT7ovVef2/J8fZuN/rDef59Pz9ezqej7l7X229725MjobP+LG4+V8uVqf
d/+8+zBn7V12j9v+CNdGZ9eX88kKQBmd3/h4ff1qtf0WXm8u3i4nn7srzcV8vXndPWZ7/br9Ml1M
XjWr5q+vVmfzpgXYdEH+9rq91Xn3G+ctqHCb7t/r+1aR62BYUIw4mymRnlPiclEkUSdoESrxRP+z
t3TdA795Mfq/N9NPm/8fPnb7ROvlfDYps/m8+58WstM4X519bOYvRm/fd1bi0Qa/1T1aa/SN+V+2
Gb/YXKwOLW5g6Ovw8k1remsyfqneDUVSSzVzRMNiIiX1xBrniMychxC9Taz0YL5uVm9WzWKN9fy0
6PZCwWdZCIV44zSRxjLinVVEBKatYiZzzfa8+H0W2xhLU7GCBJmwb4vIxGaBZzU8ipRikhSPud2y
69nbn9RKnSkNzADSwbcraIjnTJOinGOFcpf4/mLc7MFHQboZj6eLzVPD+k/Lq+kDsG43OfZiF6YR
Ez5tzj6sZojQWVIuTDCkJI+9XagjlklLorRCcOaxt90WlUxejP/yEZuqzztMHqSNe8P0Nve0QZre
Zp7Z5MUImW/RXCH4I3w0F5fz2eIfcY5Qu/0bCHpfzmw3uSAtxx+u4Oeb9LaazpsNsuv6cnaNPbi6
aP/Y6uXkZhXgjJ0x3faHO7bxb+eiB4Kh0c7TgP1RvCFSRwnk0Ei4VEaapCWnqd8fh1FAMReo1EiG
tN1eXjviVU7EKa2jMYXluPP3xXWbBr9PFGAqcha9Io5ZWIlYQBznkZjEXLRepSB9b+VhFPhJrCya
xsQQiCNiMMBvHQlBa2J1Tj7qkmkpeyH5gShQnY8fyk1+cjVbPLCLj8OSqsA1R2KJsa20FL54JbCt
KeVaF1GME/2CHcJSmuBZYIXQIAFLmQPxQhlUa8IwbVQybW7bVnjfEZYiW88lEMk8Em8qmmAveqJN
4Y5rxbnJvZWHsPxJrLRMuRijIFw5JCeROXESEceHrItBgNBmP0Y8NSxfjy+Xy/mjcKlCsjwbRbIK
WDGjGcoJG0kyJSlrs2cateO2nDjEpbZFaM8iyRq9A2BJUTQpoJyjIE1OYtn38/T3CZcJ/1FqC2Ha
ZUT17BDQqSTGcpGDTsUU3Vt5iMufxEplnWUZuY6KtoIVNiP1eU+oNDJGVBKZ8r0g8eS43HyYIOWv
H4VMmk0Q2VDgMQYE/9StmSbUYMWC8z5HhLyjyBTCqChQ/kfkbWRIuMWX4lFQaocEKdAy7TdF3weZ
jFNhMs2EZmRzGZDNffKFaGELKhlqTYi9lYfI/Ems1CIpmRkjCmEFpRUSmU3Wk5ypR5PFaFDqmZHZ
vHv3KFhmlgT6bOQ0cCaoLw1wpSUlEaiMrMicku0X7DBgci6SdzyRFArqAGQSYikikS4lqGip4d7s
eeI7wZIx0D8SOHQZATOBIvKMIsIzZYPlBsX1bRV9CMufxErlRYbHGSJku/kUCwTFv0K5qTJl2Fs+
Pi8s30wb0C2rxwXM4LKmGr1hSN0mM4m01SFqE7AgzAdR4u2aHSJTRk6zUokwz5E9ZLb4ZFQkIJk4
LR2NDB//7iWmKqUwXyzxUmEDiZYZcIWTaEVEu+AcurR+/x0i8yexkiabpHSMJN1ZibSA/gG8Huo1
T4vKTKDE3CW/FfjnwcKcXeOCX6+X45epnuRtP/SXrtdvv6Lxby90XGVcbFomYPcXpuvfOh73781q
fXNtfPnn5tMNP9hzoOPLV6vpuzsXJ7MdL9osZle/L6ZbUhFM3PhyR0O2P/rtIwj4lm+cf5x3VGjL
j7QPNPzTN91W2+3cPMaWdj3ehOXoMyKuQbIVLeeNRsUylAjceEtFBlMqbtuT9q/1Nrd8d3vh1qt1
pGv3oRuvvmpWqIUGbv3tLexbgIsf+vrl5FPHurR/7Nb5rH+QfQ+0Xhk4X+9cOHR+d3Hrqv1Pn+w/
ppD0QDSiCw3IBQlxIigjSI5Za86YGLIOX/CfqWL4Bv7zW5ruAJf7GfL46pucRRAgSaQUiBwKXwIN
MMYU3/4IwWNQUD68+o98+rur//b968vrwcX7V79f1i8CXEnhKHWgBzMYYilQZ/kMEx3SuaCSR5uq
AX6yifFyNgeBd7s+PcCHF4+ZWBVd6H0A7y++/TCfTzd50bydTyeDyPNtwG9jsSVkgaSKUYv0Hp1y
QZ9ldChCIPmyAfH+BfDXEVsH4MdQaz8u9KZ/ERkejYbMMoB/0uAsqMHWTZGTLLhKKOmipLI2oTzy
6QcP3yHjXvAPodLlnXrwG7RSNmP0k71Hw+hYJpggoGukTqG0Cxzc1JOZ2IF/uD7HwX+PiT88+MES
CRYRSVgBqwDKi5EQNYowYSLaFpPRFNX6to4+OwC/GOBnW5HURn4A1ghtERZRIaJnQ0uDASm+YECq
wcdGzJae+OkHD38c/EP8nAh+VnKJsIwojRAldUuVK0EJ2pggPOVgy6uT28kL1IF/uD7HwX+PiT88
+BWIa5/QHFqWEDsLinHQT4WAtGcBTCfG6wOO9OHCoY6jOwC/HODnRPCHlLNNaAZ9uwNAAqIc4AVF
GzPFYSAGhQBYwbpW4pFPP3j44+Af4udE8LdLgOqTg61oI7+JSG7Y8iQVJZjMXkhx2xN+ITWfbGIH
/uH6HAf/PSb++OAvlvmIYOlFAEciJEY6oBnAWnEZwCkzzQZ818Pgr6MBD8CvBvg5EfxSuQRBjCGG
gj8GY4BNnCHq8MID/Vmh7atGxiOffvDwx8E/xM+J4PeFWWcg92GcY4Eopt4guDXBIBtEkAWRrG4Z
5C+A/2QTO/AP1+c4+O8x8YcHP6ZgQkF5gVwqUfYYlM7BCRAGPqOi5ElhPF0dO6vIxgPw6wF+TgS/
Uh6zLYciQDHZIqNVNEV0vZBDsRIE1aY+8j/u6QcPfxz8Q/ycCP4SjLAyQbFoMISUGUw6UgCqICsd
aGoM9lJ12VPHBg8WqAP/cH2Og/8eE3808G+Jk14oCl3c8PuBatQ6j0qIQoygOHCVIlSjsh1pRJtD
VFJpM9AyICX8/g562dW4k9lV5tfJdN1pQv9dmdtx+99Xk446az+6+76Nd+2f/SqF6rbNgEMGTkB7
WSz0UhgWRAizDG/LKhADQoVgpNDc3+k2h06o1N32Tqikg5/dCZ4hyxRocyCvRO6xbW3puCUYn6Ad
scKyMhg53EFCJY3QO6Gyv392J9SpA/oNUEkc92ZXdmM7s9Fg7W2Arc71Zu/ViT12U5PuM3XSiXu3
SJ2c59GOqcTPzjGgXY46plKdtecYVqV1utcxGoJWtCseol2PsoJRNGfGQsKXDc++neHFsFdWDGNH
JS/b46eSE965CcOIPTc9WQCtmxE/Gh2VpezObFSne2YPt03lyH8fHVUD9HvRUTeifLRjKsucnWNQ
uRx1TOXEec8xdfPbex1TAkT9gDMxGWNtyQuGotFIUoqGDhqtvlfHt02l2f22qawsd256tm1TJ919
NDoqo8sxs4fbhtcpsffQUadrvhcddVKoRzumsm7dOQZUz9FtU6ls23dMlU7sXsfUHWzpHXNqbVpZ
z+wcc3ejDBFTeYJnzzF152HudYyi3Cqo7QhNkL1Ihpm2VZDiucgkNFkQs4r9gdEwDVfmlz6eVNI0
x9z0ZGkYchKIZhJo06zQQacAZblFG+2TjZEpRa3cZ8iGTqisTXsnVE4pnt0JVuiUAkR9FPK2tpkD
TSiUREcrIJyFKk8aCMEGHPnQCZUboHdCpQrl2Z2goWaHBAIgoN2IGJNUCxkY4TornXDmAvl1zwlQ
tAz9UNmk/vB+SEkIJ3CkzgUNekPgSwBhhlMnquU3oIST++Oer/RDpapmh4e7fcyTRQYTnHTOZGj7
W26NQS1gHQQP0NCg4sKJDuyTh/BQaVePh0p9zs4PdxPJ0/kBovqcwKDatvaUHofxrLYgGjUU6l46
iGH36a6vxMOJDdvdhv/J/EBphLwZkyCdBMagLeGKuWIh4HoMw5FbjIm+qR8qBTc7PNytuJ7MD5aG
5HDkCjqyBDyAICGu7eOhrClZ26S42Oec7+Ch0q5+X1RKd3Z+eLZ9AWFZgLAAcTLixI8UAiegOCQi
MWQ0eQLRIe2Tf3f8cGq+OJH/u9vRPxkeoAxIMWKyyAoOLGP6zNujfZEIMMQcGyNIvd+WfqUfKrU4
OzzcbeCfzA80cmss1KE54uAAhsMggxWOW5mEo+kIkiCGH4wPlXb1+6JS1bPzw7PtC5p1iAGn6XCq
EzNnnOmB1E4JiI8wFFDGQ3OxX1F/JR5OLKoxsGpd+PQTkgiZiUI8JEk6xEkB+br3MRIdcAYb8yKc
qP6m+6JSprPDg3k2P7TZoj1loqhBcxFZe7ACU3qMJyWUl/g27w+R7+Ch0q5+X1QKfnZ+eLZ9IWKw
lltKDGRHaDdROjgF3UJWAEjwHoTwg/Hh1HzB61RBOz/gnT7Psy+MFpFznBxGQwU8QH2LDIozivCF
5SmbBI7zobr6VD+oOgXPzg/uufyQoVLUDF02kmc7BYFu0ee2ssTpOpwtyBEHdh/yQ6Vd/b6o1ALt
/PB8+0KhwgP7gsCIEkoyCGxD+34Ap7nBKfOCnfFN98WJTBR7NkcYKqiIipLiGByBWIGGM3eHySPO
AEPcEb5p412p7tkBgj1b5413JWTtKY4aYBaECAFdlw2QWmicgi4sQLkWH8wYlYb1O6NSKLRzxLMB
AmVjq7/yBCcxwcRAyIxTqBibFuURKPGephzjQxHi1Eh54syHPVHr3SlxhvKbt++3/PW/Lpd4w9mN
cmj3KpzB24LuvIvnC+/6obZ/1w/uuL6+fXNdxuuHti8RWl8fvOKn/rZneM1PKyLAi33QheDVKX+f
rg5uV/8uPBiON4IdeRlQ/9q9X/8LAAD//wMAUEsDBBQABgAIAAAAIQA4OUSP1QAAAE4BAAAhAAAA
ZHJzL2RpYWdyYW1zL19yZWxzL2RhdGExLnhtbC5yZWxzhNDBSgQxDAbgu+A7lNydzHoQkensZRX2
4EXWByhtZlq2bUrTld23tx4UFwSPIX++hEzbc4rqg6oEzho2wwiKsmUX8qrh/fBy9whKmsnORM6k
4UIC2/n2ZnqjaFofEh+KqK5k0eBbK0+IYj0lIwMXyr2zcE2m9bKuWIw9mpXwfhwfsP42YL4y1d5p
qHu3AXW4lL75f5uXJVjasT0lyu2PFei7VGPIx46aulL7YSNbEz1Lw1MS/IoIek40FF++w6/s+h3P
50Y1mwg4T3j1hfkTAAD//wMAUEsDBBQABgAIAAAAIQCcqrxcIQEAAF8CAAAOAAAAZHJzL2Uyb0Rv
Yy54bWykkstOwzAQRfdI/IPlPXWT0rSK6nRTIXXFBj7A2OPEkl/YLoG/Z5JWqKyQyu6MRzq+uvZu
/+ks+YCUTfCcVoslJeBlUMb3nL6+PD1sKclFeCVs8MDpF2S67+7vdmNsoQ5DsAoSQYnP7Rg5HUqJ
LWNZDuBEXoQIHpc6JCcKjqlnKokR7c6yerls2BiSiilIyBlPD+cl7Wa/1iDLs9YZCrGcYrbC6apZ
bxpK0jy/cbpp1tuKsm4n2j6JOBh5iSNuSOOE8Xj5j+ogiiCnZG5QKSMwj0Ob6l2bwB5VviTDg38I
LxIs4O+yg9ZGwiHIkwNfzo1jFFHwufNgYsYiW4Vp0lFVE9swcz3xe555NbE88+NUNPtVz/WMfP0v
um8AAAD//wMAUEsDBBQABgAIAAAAIQAbotvGZRMAAIXXAAAYAAAAZHJzL2RpYWdyYW1zL2xheW91
dDEueG1s7F1dc9tGln2fqv0PKDzvUPym5IqSSo0ntVPl2KnE2X1ugaDILAhwAUiW8+vnooFGH6pv
g0ATZiQtXmzxA6cvTt9vAJff/fC0j7zHMM12SXzrT0Zj3wvjIFnv4vtb//fPP/392veyXMRrESVx
eOt/DTP/h+//42/fre/37yLxNXnI34cbj1Di7B29d+tv8/zw7uoqC7bhXmSj5BDG9OkmSfcip5fp
/dU6FV8Ifx9dTcfj5dV6J+5TsfcrEOEAsRe72Pce4t3/PYT/Wt/6D2n8br8L0iRLNvkoSPZXyWaz
C8LqP5HmtPRkclWewNV/hU/iPol/FSRK5H8vzy3f5VHoPYro1q8+9qrPr8ovrMMsKD//PQu9PPGy
bfLFE14WkhBxTkjeIU2CMMu8fCtyLw0jkYf0IqEvBfSNlL6xW4fCS1L6RrgPR96H3X6Xh2uJtnvy
PoSPYeRNCVkcwmzk/U+S/m/m3YVZ7n3Z5Vsv24so8sQ+eYjzzEs2Xh4+5SPv9/ghK0DohbdOaMk4
yT1xOIQi/U/v7qEQpSAs88Sj2EXijs5zt/FoK72MYIOtV9KSjfzqVAORf8jy8rTpby//eiBVCL4G
UejTSe5u/evFeAzfrr5Rkp7E0S5WX7wZl1+8KvQHcTOxP7wXuajIpb9+TtZhVL486PUPubcvPii2
mZS1FGWdBGp1/HwyrjbzkP4W5t5hG61TUvLiq3L9Q3VOR8dMHI6ZOhwzczhm7nDMwuGY5eljKgK1
XjzF+LfepDntUpYG1X6R1eTFn7Q1xbuf0nIbi7erv5XOPcUaY6ExigNrkEkXkKUFZNoFZGUBmXUB
ubaAzLuA3FhAFl1AJmSNaneOmF02oJS2C/t9d19t2pdtEoXKutbahuURxxae5V+jcDD4Y2c0GNWR
EjYZlasSHqtdEKWDDh7rYBGYjwLiEHRktBqCDoXqVxd0jgy8zGs/UvroxWJPKexH+lfliI8i1QnM
9mfxVCb30jhksrr9JaVKR1YE9ZvrXVqFPhHv9p/iqmK4S0UcbFVKWnz04ZHKrKKYiB6jOv/EJWWO
X5VAlKSerqLK5Pp9EjzsqZgoSylZY1All213h8z30nd30e5w61fZnFj/QWeowrNcsMrpt0lCNYzm
ROXAVBboNylP2jzEwa1PYvueSKlApNP3vYRWoJrHL88vpkKvWi94DqtS3iNYSnrE0y6jemLrEWfe
If8sy4tYbhP9Q5lITgmjR8leEMu/KGcpJaHXz9dX2ymie1WnJPtDklFdpZJaQdVm9VlxJuWOjiaL
pXaEdHRFDZGZg2LIl9XBVPnsf0ri/Lc/SZ6ENo1chPyrUKtb/xeR0sZU+MuF0oYAIaLqyOLECeL4
QFK3koov9KEI6NR1jYUgtIZcvhmkWKEBpFijOIdmkC/sWZTCnzpW2kMZtjWp8vVuU5J9pBeUx56v
FxHtujLZco3OejFbXtcGeym1OKFZ/9juojWZg9Rci2a1hKh1DL9Pb/5U63P54lgzC5MvqK1832mV
/jEoOh4kcr2e1unRZHnNWwer2CaU1uzR9Ga+ZKVi1duEAqnmyymLxCq7iQRCLeYrHuovsf7R9NpC
N8uR8mLcxi3Hk1l7jgwkoGg1mfAqwFKk9J+TaTFe8TKxymQgaZnYE2MpMkBQi26mKxaJVSMDSYsz
WlzPdHwqLU7mK3YPSjX8+R5UR3bV5OrsQcfLyU3BgZR0cKFlVtDkQtEXy6B8nFhILaFsrFZ//D69
6e67Eai3hZVSqzyxY9DAEwXDWs4tvoe189I/IxRY1my+4m2UtXYTCqSaTpdS1WXBgDkaa+4mFEg1
Ha/4UMa6RDMAaalY/9NAE4ZpLRCL0sAQopyQpYEcRDkhC8uLEXROiMLSYoBoSUbT6YzfcZYaA0mL
M+ovmi4WUz4GshQp+6zdCcj0ZqPpfGGJy3aK0HucRxEigSpRnshvG6tKRhQAmebdsw6LTIqn9nXb
qt+sQ/UMOmYd49H1bCxTyiHrgF7Eq8066FJA7Z8wS3md6Q6egS3P6nzGyo875lm4HrqShcUpsZGy
TEMQCvzbcjLnizzWv5lQIJVLnmWRarLsEizNlA2k6jEnna7KmslIJBu4QhcOUrlwhVCwgy5cYQan
pWKzyQaVQhQtEIvSwBCinJClWMToh5pZ9glZ7NkEaqMWZTReLObsWbHcSJNHJC3OaNmlE2YAgUgO
yYRFpJnl3FiWGlLl9gQZIEDQZLWor+McdTBZBTKQgKH+Mvf5zGL2LEXK49eREWR6s5l7JyWSDKEz
O48hRAJNmk/lJYJ2vrpJJAdTs4hUsdQ+bb/uN21XNw10TtuX17JXPmTtQ9ZuucDUJnmeD+VCdWHa
WiCp4NFULnwTqtsszIa7MvvCrQVvPneoUxAK3PnyesbnYMVylpwQoUAql9wboUCqyWzGd4cauMI8
DKRyqVMQCqSaj6cdkqiL1XQOXGEgBa5mK8sF9tyuDAgFXE0W0w6t4sZK00WvbFI5cNWhjmqgCVE0
TWxy32B5iKL3jUUpFrHYL6KckIW1N+nU0HK1KC41HSJpcUar8Q3vA1h+mmRyyDQtMk1vxvzVM5an
hjKK3TJWfQwQoGhGpsEisRQZSLBt/VV1lIx3oEhuGxoryORQ1SES8DRZXb+4oqWbKkmeMCydxxMi
AU+L2birySESyORgcogEMjnwhP4NZHJoNyGSlqm9ycltQxAQx4EiRNLi0I1gpVdqXwDf9FsAq8eu
OhfAN4vZcMPh81thh+tWeDPJNynLVCg8l2q6b7fuR6Kg1kK0r4Ux0LVaWHmicytg9NIXXRgzsosu
3HmP21DN5otlFYTrQbiYXi/4HIbNGk0oiBerSZe00YQCqegiGX+7b7GcpfzAEwSpXGp+VAqQajnh
hWqgCpFAqB7bIy5UWaRyoQoNF6hazWf8deoGrhAKuOqxPeLClUUqF67QuwJXdLmys14hFHDVY3vE
hSubVEN75NmTG3rL2KSfdePS/6PlggrNppYr1ay5GUhanB7bIzPbHZTFaoYPb5JJFSLGxUGWJ5UL
1RkU8ETRjiWcpckAApr6a5HMpzd8R5OlqVGm3lokS9vd3CxPcutspt9bi8RBnSwydVMneXYYBECd
HHhCJNCn/lokDjxZZHLgCXMwzVN7k5NkI8gxRbzxFgvx/gSRtDgjB4oQCWRyoAibLShTdw+OSFom
lmw7RQiC4nR33oikxWnXRQqj42enHZ9AHo+Gzo98dqn9o6ZY80oreknPSaFjQkG/eR8E06yLLox+
5qIL04OOdcJ00YVXf9XCmB20OmMZnMjLDb2uro3UzlrdF9WdtbqvhTtrtfvCOOxDK2fjYAO2biob
csgY5AN00Zy/n4etCEwoSAjmS0trqFjOyONMKJDKpUeBJwhSufRzUK1BqqGlejQApdxBjKjA1dBS
LXRQXaorqcKsB6gaWqqMWmEQB66GlirDFRZomiu2Xmzw6oii/SeLUixiceiIckKW0kCeTUYqLQVR
TsjChjwZdC2eaWipar9kdB31ltEoHL4rw6qQAaR3bTS0VDXhUjHRtwFP/d115tAHs8jk0AezRLmh
pfpMC/gkk/W3rMlJVUIQUCXqOvPGy3puAwm8gIMqWWRyUCVM6bVMHSlCEKCIrI0vfOwUIZIWx6Xr
jEggkwNFGClRpv+XXeei21yN0irna1Yv1AzF591oNcrTGMZJ94MwUxdpqFKvQ8Mcm+GTYRwnk34a
GcgX1k8U1makrsax2+LY9rfHTijVPH+a3DCP0zIsVsYndHSturpqU5vu2Uw5bTBLEHCsNGiQj6xs
jDahjry95d4ONgKZUCDVBeZxslQpjusrDLzRsdwYxyI1r2kMJ8uMobO4W7YJVCxPBpLmifVwrPYY
IG3EYX2lgaTFcRjDScbUg+cc5nDa5mwbu9Wb60QgGVFf0vVlrGZRUOv1ZcVT0yVA1s5Lt4zrgWXN
Ftf8s22soZtQYFqvbA5nA1cYyDVXrCtroAlRNE0sCusQm8Ipi8I6QxPlhCwsL0Yk7CuKvqbxmywz
yiwhv1CD+kfW5+FYrTGQTmwUqzQGiNZfuzis4hhIWpyRGitp3BVrp2gK91qATP09Efs2xm9OqO9y
fp2ms41h/iYT9NEzt4q9yv01FWoI9OKyDex4o6DWbOMvO2PldprSHDwDG9Wdz7jNwqx7KyMsrof+
bW4ZVMyGABMKnO4rm7/ZwJUlFvSYi76y+ZsNXKGv0nrF5n8NKoUoWqVYlGIRo/1oZpEnZGFTChPl
hCwsL9JQLeZ2M7c0rFhuDCQtzmuav8mypBw45KXcLzewvBjHAi+vauwmy4xy9MDM28/YbWM37QxN
+YTdoaZBJNCktzB2c7LqN10f5m4e/QafYaqY+b283FX5zXPLBAxuFz3jOdj8RRdGF9FqYaUZL7JM
wDOw1SedqW5zxqw3L7MuXE/nbiN6/r9z/xehwJ/3OFjCZT6iRSrrzfUNXKH9AVcu9QlCAVc9DpZw
4coilQtXaLnAFf0qQ+eL0AgFXPU4WMKFK5tUtsESDXqFVZjmiq3C8nZVmKaJRSkWefm1HFqupmXk
UMshkqamx8ES1uy3WM1gWrpsi0zWG+lY/VGZBdQsvVRz/d34fIFxmywzkmO0UdAg626x1mUggQb1
d+OzVSa7BuHZgUzdNEieHfr983hCJJCpv1kSDjxZZHLgiXfULh4JkTRP7Z213DYEabNtdlVCJC1O
u0EJRz/vPbnut/Ad5m0O16noV9HRhLGK+ua1Pkbpiy6MjyJcdGGMKq0WVq5oKLlfxAwCNhsqa33U
KYgXq0n3YX8IBQHjlc3bbOAKDR+4mlnGq7OZo9lhAap6bIu4zBzA8wOpXEp9dM5A1XTR/Sc2EAqk
6rEt4sKVRSoXrtC7AlfzefeRtwgFXPXYFnHhyibV0BbpNm+TdU0y1KLlggotV5afYWRdk4EEGtTf
z5E4PIKJZwcydSvWmLZIfSl31eUpGAMIROqvR3KBeZt2dUKDBXWybp1dnRAJeOqvR2KVqViN77JZ
ZOqmTtJYMAicxxMiAU/99UgceLLI5MATZoYd7gaXHOOxx8zwFw2KfeD3HZHa7JZdgxAJZHJgBpss
IJOD40YkLVM/3SMH5eHFadc9ev5g8031+PLwAyvlwExpF0hwq5pcxa1z7zhA93nRhdEfXXRhzD8u
ujD6mYsujBMdLrrwarir5FX/misqiwzDTKu6s1Yrf3duN7GzVve1cGetdl/YYcxmymVMZZ8KGYP0
5Ga85H95kS0ETCidn4xe2ZjNBq5QrYGroZPKzPjDiApcDZ3UsuYo07zSbjDrAaqGTiqjVpiaAldD
J5XhCusHzRVbLzZ4dUTRXp1FKRYxCvNSxxHlhCylgZw7ZpN14zLoWjyTQ0GOSJqaHm8wc6jKLTJ1
a1yoQhJuMBs6qaZqS3WyeCTr1rGmZiCBOg2d1MKrQF8E4yXwNHRS15igHjPzFjqpx6VK6ztepW3h
scDMqxqqaQ9pfHQdOYQ0RNI8tY/2kmwE0cHeZe4oImlxmnvMtqGazPvFxBLyLf8UwdaLxT689b+k
4nAIiely7EYWRhvfUxX3QaSfUxFnVbP6+EgRBHSt99fwEApy8XB4OZgzEl+Th/xjsg6rlcq8yPey
/GsUfriLbv27+9+2B6MRnh0U+9lWHEIv/3ogObfhk7hPYt972kdx9i6ld/L88O7qKgu24V5ko+QQ
xvQZybgXOb1M76+SzWYXhO+T4GFPkl5Nx+PlVRpGIt/RLUzb3SGjzty7u2h3uPV9789P6TpMP202
t/7f1XBRsf7jQ5aX50N/e7v1061PW/QoSPoxDeCufz+w/pSio/y0mAE6l88WySmVCkm+kOdVoh7S
MPu0UbMsNWfVcMuK8eevzG0sosV0rLahyI3VHsa0A74XxHkhuWVrVPqjt6Y4Sk0/fRRpzUKw/Vk8
Vb9worYp2P5CeRO8KU8Sj9JXOfInddQlN7fewfo86v3C3VzUz4LVHzttJxrDkdeulw/wHrroZ5He
W5rEGxHQxo1HE0Xb0ZGp85G585F3bY98Pp21UNr0gUxfWVTxorJu7LFK86HAXp76R/HR9/aC7K74
SxlKjSMXsZuNxVBo07tOGbLajir3Kh2L7qtTeu7GLu+3FFmcuymIKVWJtqPS0WP3Lj0KqT+Z9q1/
5OsVLtJe4OnXlcdXsfHYrSgm0UGAWxnXmg5upX7z7iGKwvyfsbiLwnXlaWutQMQjl1P5WpGKvdqd
7eHz04fPv/4Y7e7jf5C7lFoX5KlanUJf/eX86ceYJlCn/x2mef3d/W5dL0yLlUu8fadGFz8+pb8d
ZQkywlQ6VOtUyceRtxr8nH9hP0eFYzs/R3ZeZgiesk7DmEs/V2dGb8vPlU2ks/wcUTj4uW+RmVeB
9VQq3nPyNvi5P1UKVmWfLzifo2yxnZ8jh9jOz5mDI99EPld2hc7yc0Th4OcGP0ctmSGfu3Q+t2rr
5yifaefnzIlbF/JzGNVVQXlGoXp9fqFKnA2ObXBsg2Mr2u0XLlRv2jo2yvTaOTZzosKrdGwz6umf
24EjzgbHNji2wbFd3rHNWl9poMqsnWNbnrxg+o2uNPSbsc16uLRAnA2ObXBsg2P7lo6Nv8JaXh54
H26+/zcAAAD//wMAUEsDBBQABgAIAAAAIQDlqp0b9AMAAA1RAAAcAAAAZHJzL2RpYWdyYW1zL3F1
aWNrU3R5bGUxLnhtbOyc3W7bIBTH7yftHSzuVyftNlVRnaofilSpqqouewCCsY2KwQWSpm8/wI7T
qanqfCk45iaxnRwc+MH/HA44F5fznAYzLCThLAL9kx4IMEM8JiyNwN/x6Mc5CKSCLIaUMxyBNyzB
5fD7t4s4zQdSvVF8i5NAF8LkQF+KQKZUMQhDiTKcQ3nCC8z0pwkXOVT6VKRhLOCrLj6n4Wmv9zuM
CUwFzEFVCNygiBwSBoIpIy9TfBdHYCrYICdIcMkTdYJ4HvIkIQhXb1Aoc+tf4Xn4MiXo2VYjlCQv
KD4FQ1s1RRTFwQzSCICwvBRjif6/gqC6l6r8VB8H6q3QTVQWBIJCEN2iPX0nU0JoGuy9gUSY4bN4
eAH15RwLqA2kigAXKuO6RYqMoJHgTBlrOKAkzdQTSQNBNBmVCYwfFQhiIvRZfYO60BrP/YQGTJcf
AcZj3KuqV39vfzcvzuKq4dT8UVSHtqltddiT7jcknkfgTP8mOJBIpJMbKgJdH90JdR3168S8msbT
9TcG5osJobS27TeyrUyMNU4SjNSa9rWRvb9GUtvnhHFR/Qbd4bGpgO00VPWrH56U3696QNUAtjfY
Y82n7ECLs4oWZQ+al6mfRbn/zuJ5lR1tQ14zzDyxFSPMKM7Xo3uDEabmW44wqCXVWWTGD33dbF0T
RePCvCS2xYUZWot4ygccZbDTLGDZQA63DjgMLROK+XBjGd65Teunp9WSYD5J7/L0kULkvVctbbvU
QxO51zPe98efza5s7OeZVFNqN5hM/CAxiY0Vc6jduKG1B4kkk7GATJ7euhhzN5tYdm2GlKR/PLW9
DqNyXr7bZN/EUyuzunsTv31QW+hj30l9bOY0VutjM239PK3ezL4OOwycKu1aRoVlWn1th4UgpXyq
HJwWtT+dtzYMKKUy/cDnE9qQTzC0XIzy/OJgOYDMSvtyKdfQ8pnVtuTBDS2fWW0TLZ9ZbQutAoqb
zOEshfdfq/zXe2rej7VxrHl/1kZq3q+1j5qb2aVtEhrNskPOZZeWTqt/66LT6jgSFz1Sx5G46G46
iCRJrxByMbm3DYtmyxvOORHE2cjj+PggRrOYYOcrRnYfkB8dH56LORAOJa7MpnxHgTSTnNUrqs1s
nZOridcqZ7RKckpi7zxWbBE5kFpZIC4LVgfjK8vk2quWM6plJx8jrjfmvD44+vRWB4fJIu71YOzD
5O74FBtweSqOUbEiZqIM1zazdVC6LAtTb89iubHwQBGwZXFsifeWTtUti2PLuLeUhX6eIysclKhm
MnFUGaz4+drDcCe/62E4NDN3U6Tav2Vy7WdlBJ6N50fmL5r5msOueCz/NED/i+HwHwAAAP//AwBQ
SwMEFAAGAAgAAAAhADics8UVBQAAK1EAABgAAABkcnMvZGlhZ3JhbXMvY29sb3JzMS54bWzsXFtv
mzAUfp+0/4B4X0m6ttuipdO2rtKkapq07gc4xhA0YzJwuvTfz5cAJg3hYpMGSl/SYPlgf+f4OxcO
+fhpE2LrAcVJEJG5PT2b2BYiMHID4s/t3/e3b97bVkIBcQGOCJrbjyixP12/fvXR9cMZjHAUJzfI
s5gUkszYtbm9pHQ1c5wELlEIkrNohQgb9aI4BJR9jX3HjcE/Jj/EzvlkcuW4AfBjENpbIaCFiBAE
xLbWJPi7Rt/dub2OySwMYBwlkUfPYBQ6kecFEG0/QEz5rS+d947cgvzw1nhqX4ud0YBiZD0APLdt
R15yUQKLVyCgdwmVo+x/iz6uGEJCIhNlW6s4YJBOppMJl+EIxJQpCX3E6G6BLQJCNo9ELppsb+8F
GH/FMZNuhYgu53aMVghQNgpmAljERuViAISI0Gl2h3yqXBgOSE1JWJGSzZJCkOchSKWcLRx0c5dK
zq7cZsvOLn0rzhQgpBuXstNvCgypFvK91IThnMNQAtHbA2MXB8YuD4xd8TGxp3yp/YQd4MAnP5gJ
vijs5eF5Pqt5vmOGX56yT4PfHhDRhZ5THMCrJZAe4HLC/gQRKcx3gAa1pl8IH9T67pd6069qTh8E
JfOA4JxtmMcNqntRtKyGAILFBrPxt/U3Lhz7YDbOD1hNjYuoZRAb9/zvof8TA6gTfnBeowGh7Vmx
7WxJim1nS05sO1tSYuXsEzKTylheddR0kycV8mDQTeOcQgS35RZW6it5LNzepjh3q7NZxrnPT79o
zSwOHPxRLXVrAMYPTBIs7mNAkvObAaaD8BFiJM7mTilFhfH5MjPP/zWin5aWJOU3Lj8d38UsRq3N
dM/M8bWW8tx0iDxXVTPub9mr0lDoJi+IG4kgIcA4WvMCfDE3q1yJUmHUDPRSScWgsqT4I+7V+Xk0
DjNIEjo+A7E5DHqBl6atnUYkxGFoUAAbTh2Ib7xBAWw4dSC+8QYFMPFobhCmvgLx16WRlOuE4Kh0
jirNGHHTKoxPmaNyPdLFDodHVDie8klNOIbDLiocT1mmJhynxjmVy+7wkOmmTZpcJY9rHun3L97N
LXJ6o8FXxZTgQyd15jT9SBte+g22BhsWwX7XIdi5I+o32BpcWwT7ULKrPiHnOXoGWSVBSsvO3Vw2
s6vio/G02fM/Q9giYeSugSOstHN0Th4l3QvC1kvGhGpKxup153WuUvdP7oaMRLIwIrejVnkP8W6r
a95z2T+tigfR41kdmFZp/Jl3zxrT60WHMUVOU/07PYuREIdGiEmEA1fH0aXpiG74N+CHYcZjE6E0
Tcob9VaZl3Sjty8aJDoq7fhKE7ndbcQeP//TfD2nmEtnQUad9K8k+eJVFLXD0YRMXiwwLXO3w9bE
Onf7bhvJNFL43O0vbbSCUaN7EksNyxMa7V9InSakI8Fw3VfXwUqOzUgwPDRQwOFo7nTXyjLr6DLy
VzUaEXZPCUbk7CO7jOzyxN2O4UtLJihhVv2AtH/hi0iOWjSMmnrwVerm+lxmFaC26EboHNT8Deee
WmqLroPOQT1ik4Hxgpaw1BbdBZ2DesRmAuOgsjeZlqv2LzpwStzj2IrJgWb9Q91z/6jA/fNFH+Jk
CdztD1R107JxVIz5gUyLy0baJ2ishbFsi8nNOGs8KtQIsvzNqHHLPPmIvs44+OwN1lNnEHXP/WOQ
GD3cb5pT9B63t+d3gwxw8062W3aTzoHXa7QTQMjfB2Q/cXj9HwAA//8DAFBLAwQUAAYACAAAACEA
OvcJTIUHAAAMQQAAGQAAAGRycy9kaWFncmFtcy9kcmF3aW5nMS54bWzsXFmP3LgRfg+Q/yDonW7x
EsmGexY6NwaMjbEzi80rR0dPJzo6kno83kX++xZF9bkJbI/d8zCRYbRFiUeRrONjVdFvf3iqK+ex
6PpN26xc/MZznaLJ2nzTrFfuL3cpkq7TD7rJddU2xcr9VPTuDzd//cvbvN8u805/hIoO9NH0y3xd
r9yHYdguF4s+eyhq3b9pt0UDX8u2q/UAxW69mBrV1YJ4nr/IN3rd6drdd9Jv/9RJvcm6tm/L4U3W
1ou2LDdZse/GdCIvO9F/6uLzdNR607g347T67V1XFPa5efyx295uP3S2mP30+KFzNvnKhYVqdA0r
4i4On6aq8GJhluei7dp2NFXvt07d5kX1Dvr6PYmCBHMlkJfQCDHMBJJYKUREID2aUB7T5D8Tdc3j
l5Jj6h1pObbqx+no5VPZ1Tdv9RJW1HlauYRKJRR3nU/ACERwrISZm14WT4OTQQXsE09S7DqZqcE8
KDFTY3Hsatv1w49FWzvmAXaheNLr1qyrXurH9/1gHtb5tHI6/6frlHUF2/WoK4dILjw74qHKY3la
A2PObBUYcuoPnvaDms77ttrk6aaqxoLhwiKqOgf6X7n3azyRe1arapyPK5dK7MGeZhr4r6z0AI/1
Fvamb9auo6s1SEc2dONMzlqPnH4coxrwWOdhV/wdlnUcd5pUr4fLV9Wuvnylq+2DPn8JUzyMMq72
GQFm9rHuH+xY46dpllVjFqEoyyIbprVvd0PR3T7kH537atf9rGGCzIM/rpNvzIaBNNlCt3L5+AU+
de3w62Z4uH3QW+B3z65Bt74/LKxpBM3MaCP5lhQqzUvLH72tPlJ/oGEsnZA3Co1lTiM+/fCpAinU
y6r5uShB6IAFqR08m0YHKoE+0FTwe29+7WhjA9OyBEY4tLUb03+m7dTkuHJf2d7OBxqN47fNcGhf
b5p2YqBzvjQ8YwkvbX1YmJMFMI/DU9jmn0yX9/AvqKB+m6Ub2Pn3uh8+6A5ECJYA9LgRurb7zXU+
doaT+3/vdFe4TvWu6WF3ifSh2nBa6E4L96eFZldHLYgNCDyMZh8JiJ+jmwzGsPIwFaJh3ApDYNMG
u6EtN4OdkqXXfKj64dZsKbwHmR1/YCK17t6PxFePMBb0vmnyooFpmIEOYufkRXmn729/W7nAVUZS
xx6b221mHoC+D9lg2U6NvLhnhGOF0G7JadU9v8C749egHLXUaT3KTzh5m0017nc/gUm0A00atTPE
dDCrShv7WTTol1tYPaAaA9mu86+iM1YWpMxsuF4ON7fhuzujQGFM+IX2Rp9N+7/fdMsA/7jQ1j6X
oLCttuae4IzYPg/aGnS1z6jV1ooKqqyyPu1tkri92TuxSAEQyRIWoijyA8Q84aMwjghKKOGxDGXE
PHZ1i0RhEMHEOEffY9MMjgbJB4NAmZ0hJ77CcpKjvWXbW4bnmSPgJLuk1zFHOsuA0cnIycOmmdjX
quORO17OhlhL0bTGcO4lx/DnQTmbl6NKOiCh/6adJ9PwGQ17He38ZWN/Tjsf5mltz/+WDw7c6HlK
oiiRFDGKUxQkIUEKY0k9RiIZvwBi40AEoDSD2CZWPUg/JZJwUA8jVsNMGZ1pt/b7CMeVsZoVjr1d
PAM8M14zpmTGa5abZ7y2h3UzXttDwCvitb+1dfE8vCa4IgBQxtO1AjBzobCllB4GVW70tfAlYVZd
fxlaS31PUF+CNfI98B/4SYJUksKPp6gfkTQKCL46WmNg+hj3J0QqFeOj+p7h2pkX4Hsc+We45trD
9BfDNUE8KpMYzi9BECJAQwkKsVBGQDjHYUgSGVxdQKjggvhWQITnCzEeyI7ygV8FZrMumpc8u8zu
uxkO7g83Mxyc4eClR/CKcDDIwRA9Cw9SJQSV9uwOcRfwtp27774JD4ZxkshYxCgwoJB5CTgmSEpR
gkWqROx7KSXXN3c+AWtnES/4jfHsvlteKQQ048GvxYM4TdIIzkmI+wm473ypUMiph4BLQxp4JPLT
9PoCcoIHiacguqrOdcDrAIRs9HDPgNCEoPcwZY7nzvHcOZ77GuO5EBpvq29GhCZS79MLc/BNkJBx
FbOACCS8WCHG0xDJRHIU0AAwYcIhz0hc3eIRqojEkFAATk548KWN2B5dIHNEF6Kus4twzPV54Ygu
wSTGgLiQr4yLUESQ8QBudRSnnGKWBJS9hICcRHSJEcnX6SPkMyScU/zmFL9DjqON8s0pfq8zxW/Y
mYzG/lmgEPKwD2FjohT7vn5CzoPQjxX4PThm4CcMMApUlKKQhCFOQwpBquv7CbHvC4anvHNJCeYX
eeczKJxB4T4B/IVBYZBicM6nHGFCuBEQgZQMfIRVGMap9Knk0dVPTZhiTgRk8ppTE+T7TcnIx1PT
6/AT+jMonEHhDApnUPh/ce9j0GX5LEQIVx7ASQC3How1IFJ850zCEKLSLGYICwyZhInHELhDIE4m
mZJExCn8fVGDJwCaqsurLa8iUcre/JjjYnNcbL7naC56zk6QDvL4Xuk9x7tCw6XX7nlOkFOTJxWH
vKLzPImvCYxNV7rsDf+xMP2HBDd/AAAA//8DAFBLAwQUAAYACAAAACEAdUVNut0AAAAGAQAADwAA
AGRycy9kb3ducmV2LnhtbEyPQU/CQBCF7yb+h82QeJNdEAkpnRJjghcTUcDIcdsd2sbubNPdQv33
Ll7k8pKXN3nvm3Q12EacqPO1Y4TJWIEgLpypuUTY79b3CxA+aDa6cUwIP+Rhld3epDox7swfdNqG
UsQS9olGqEJoEyl9UZHVfuxa4pgdXWd1iLYrpen0OZbbRk6Vmkura44LlW7puaLie9tbhM/NY5j5
fDP9en1/IXWYvPXrfY94NxqeliACDeH/GC74ER2yyJS7no0XDUJ8JPzpJVNqEX2OMJ+pB5BZKq/x
s18AAAD//wMAUEsDBBQABgAIAAAAIQDSM9z5HQEAAGYDAAAZAAAAZHJzL19yZWxzL2Uyb0RvYy54
bWwucmVsc7STXU/DIBSG7038D4R7Szs/Y0Z3YWOyxBt1/oATSlsy4FRgav+9uOliE1a92SU84T1P
cl7miw+jyZt0XqHltMhySqQVWCvbcvqyuj+7ocQHsDVotJLTQXq6KE9P5k9SQ4iPfKd6T2KK9Zx2
IfS3jHnRSQM+w17aSBp0BkI8upb1INbQSjbL8yvmfmfQcpRJljWnblmfU7Ia+jj572xsGiVkhWJj
pA2JEaxW0Dowjxsl1s9h0DKGg2tl4PQbefa6h0UW5SlLe82O4PUAA25CwklvwaRPcQSfCgIkbOp4
PelyecDFKOHQYxMygYbt1vW1putxE37WVDl4jz1MGezIpMTFAYlEKf9dnDvU6HxCSGzB3oeNfkf5
CQAA//8DAFBLAQItABQABgAIAAAAIQBgJjcoXAEAAHYEAAATAAAAAAAAAAAAAAAAAAAAAABbQ29u
dGVudF9UeXBlc10ueG1sUEsBAi0AFAAGAAgAAAAhADj9If/WAAAAlAEAAAsAAAAAAAAAAAAAAAAA
jQEAAF9yZWxzLy5yZWxzUEsBAi0AFAAGAAgAAAAhAKD6Sp2dDwAAv08AABYAAAAAAAAAAAAAAAAA
jAIAAGRycy9kaWFncmFtcy9kYXRhMS54bWxQSwECLQAUAAYACAAAACEAODlEj9UAAABOAQAAIQAA
AAAAAAAAAAAAAABdEgAAZHJzL2RpYWdyYW1zL19yZWxzL2RhdGExLnhtbC5yZWxzUEsBAi0AFAAG
AAgAAAAhAJyqvFwhAQAAXwIAAA4AAAAAAAAAAAAAAAAAcRMAAGRycy9lMm9Eb2MueG1sUEsBAi0A
FAAGAAgAAAAhABui28ZlEwAAhdcAABgAAAAAAAAAAAAAAAAAvhQAAGRycy9kaWFncmFtcy9sYXlv
dXQxLnhtbFBLAQItABQABgAIAAAAIQDlqp0b9AMAAA1RAAAcAAAAAAAAAAAAAAAAAFkoAABkcnMv
ZGlhZ3JhbXMvcXVpY2tTdHlsZTEueG1sUEsBAi0AFAAGAAgAAAAhADics8UVBQAAK1EAABgAAAAA
AAAAAAAAAAAAhywAAGRycy9kaWFncmFtcy9jb2xvcnMxLnhtbFBLAQItABQABgAIAAAAIQA69wlM
hQcAAAxBAAAZAAAAAAAAAAAAAAAAANIxAABkcnMvZGlhZ3JhbXMvZHJhd2luZzEueG1sUEsBAi0A
FAAGAAgAAAAhAHVFTbrdAAAABgEAAA8AAAAAAAAAAAAAAAAAjjkAAGRycy9kb3ducmV2LnhtbFBL
AQItABQABgAIAAAAIQDSM9z5HQEAAGYDAAAZAAAAAAAAAAAAAAAAAJg6AABkcnMvX3JlbHMvZTJv
RG9jLnhtbC5yZWxzUEsFBgAAAAALAAsA6gIAAOw7AAAAAA==
">
 <v:imagedata src="hex_files/image003.png" o:title="" cropleft="-21295f"
  cropright="-21199f"/>
 <o:lock v:ext="edit" aspectratio="f"/>
</v:shape><![endif]--><![if !vml]><img width=672 height=427
src="hex_files/image004.png" v:shapes="Diagram_x0020_1"><![endif]></span></p>

</div>

</body>

</html>
