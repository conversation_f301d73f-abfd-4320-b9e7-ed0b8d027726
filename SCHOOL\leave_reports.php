<?php 
include('class/School.php');
$user = new School();
$user->adminLoginStatus();

$year = isset($_GET['year']) ? $_GET['year'] : date('Y');
$leaveStats = $user->getLeaveStatistics($year);

// Group statistics by staff
$staffStats = array();
foreach($leaveStats as $stat) {
	$staffKey = $stat['id'];
	if(!isset($staffStats[$staffKey])) {
		$staffStats[$staffKey] = array(
			'id' => $stat['id'],
			'name' => $stat['first_name'] . ' ' . $stat['last_name'],
			'email' => $stat['email'],
			'leave_types' => array()
		);
	}
	$staffStats[$staffKey]['leave_types'][] = $stat;
}
?>
<!DOCTYPE html>
<html>
<head>
	<title>Leave Reports - School Admin</title>
	<?php include('inc/header.php'); ?>
	<?php include('include_files.php');?>
	<script src="js/jquery.dataTables.min.js"></script>
	<script src="js/dataTables.bootstrap.min.js"></script>		
	<link rel="stylesheet" href="css/dataTables.bootstrap.min.css" />
	<style>
		.stats-card {
			background: #fff;
			border: 1px solid #dee2e6;
			border-radius: 8px;
			padding: 15px;
			margin-bottom: 15px;
		}
		.staff-header {
			font-weight: bold;
			color: #495057;
			margin-bottom: 10px;
			border-bottom: 1px solid #eee;
			padding-bottom: 5px;
		}
		.leave-type-row {
			display: flex;
			justify-content: space-between;
			margin-bottom: 8px;
			padding: 5px;
			background: #f8f9fa;
			border-radius: 4px;
		}
		.leave-type-name {
			font-weight: 500;
		}
		.leave-numbers {
			font-size: 14px;
		}
		.used { color: #dc3545; }
		.pending { color: #ffc107; }
		.remaining { color: #28a745; }
		.year-selector {
			margin-bottom: 20px;
		}
		.summary-cards {
			margin-bottom: 30px;
		}
		.summary-card {
			background: linear-gradient(135deg, #007bff, #0056b3);
			color: white;
			padding: 20px;
			border-radius: 8px;
			text-align: center;
		}
		.summary-number {
			font-size: 24px;
			font-weight: bold;
		}
		.summary-label {
			font-size: 14px;
			opacity: 0.9;
		}
	</style>
</head>
<body>
	<?php include('inc/container.php');?>
	<div class="container">	
		<?php include('side-menu.php');	?>
		<div class="content">
			<div class="container-fluid">
				<div>   
					<a href="#"><strong><span class="ti-bar-chart"></span> Leave Reports</strong></a>
					<hr>
					
					<!-- Year Selector -->
					<div class="year-selector">
						<form method="get" class="form-inline">
							<div class="form-group">
								<label for="year" class="control-label">Select Year: </label>
								<select name="year" id="year" class="form-control" onchange="this.form.submit()">
									<?php for($y = date('Y'); $y >= date('Y') - 5; $y--) { ?>
									<option value="<?php echo $y; ?>" <?php echo $y == $year ? 'selected' : ''; ?>>
										<?php echo $y; ?>
									</option>
									<?php } ?>
								</select>
							</div>
						</form>
					</div>
					
					<!-- Summary Cards -->
					<div class="summary-cards">
						<div class="row">
							<?php 
							$totalStaff = count($staffStats);
							$totalApplications = 0;
							$totalApproved = 0;
							$totalPending = 0;
							
							foreach($leaveStats as $stat) {
								if($stat['used_days'] > 0) $totalApproved += $stat['used_days'];
								if($stat['pending_days'] > 0) $totalPending += $stat['pending_days'];
							}
							$totalApplications = $totalApproved + $totalPending;
							?>
							<div class="col-md-3">
								<div class="summary-card">
									<div class="summary-number"><?php echo $totalStaff; ?></div>
									<div class="summary-label">Total Staff</div>
								</div>
							</div>
							<div class="col-md-3">
								<div class="summary-card" style="background: linear-gradient(135deg, #28a745, #1e7e34);">
									<div class="summary-number"><?php echo $totalApproved; ?></div>
									<div class="summary-label">Approved Days</div>
								</div>
							</div>
							<div class="col-md-3">
								<div class="summary-card" style="background: linear-gradient(135deg, #ffc107, #e0a800);">
									<div class="summary-number"><?php echo $totalPending; ?></div>
									<div class="summary-label">Pending Days</div>
								</div>
							</div>
							<div class="col-md-3">
								<div class="summary-card" style="background: linear-gradient(135deg, #17a2b8, #138496);">
									<div class="summary-number"><?php echo $totalApplications; ?></div>
									<div class="summary-label">Total Leave Days</div>
								</div>
							</div>
						</div>
					</div>
					
					<div class="panel panel-default">
						<div class="panel-heading">
							<h4>Staff Leave Summary for <?php echo $year; ?></h4>
						</div>
						<div class="panel-body">
							<?php if(empty($staffStats)) { ?>
								<div class="alert alert-info">
									<i class="ti-info"></i> No leave data found for the selected year.
								</div>
							<?php } else { ?>
								<div class="row">
									<?php foreach($staffStats as $staff) { ?>
									<div class="col-md-6">
										<div class="stats-card">
											<div class="staff-header">
												<i class="ti-user"></i> <?php echo $staff['name']; ?>
												<div style="font-size: 12px; font-weight: normal; color: #6c757d;">
													<?php echo $staff['email']; ?>
												</div>
											</div>
											
											<?php foreach($staff['leave_types'] as $leaveType) { ?>
											<div class="leave-type-row">
												<div class="leave-type-name">
													<?php echo $leaveType['leave_type_name']; ?>
												</div>
												<div class="leave-numbers">
													<span class="used">Used: <?php echo $leaveType['used_days']; ?></span> |
													<span class="pending">Pending: <?php echo $leaveType['pending_days']; ?></span> |
													<span class="remaining">Remaining: <?php echo $leaveType['remaining_days']; ?></span>
													/ <?php echo $leaveType['max_days_per_year']; ?>
												</div>
											</div>
											<?php } ?>
											
											<div style="margin-top: 10px; text-align: right;">
												<a href="staff_leave.php?staff_id=<?php echo $staff['id']; ?>" 
												   class="btn btn-sm btn-outline-primary">
													<i class="ti-eye"></i> View Applications
												</a>
											</div>
										</div>
									</div>
									<?php } ?>
								</div>
							<?php } ?>
						</div>
					</div>
					
					<!-- Export Options -->
					<div style="margin-top: 20px;">
						<button class="btn btn-success" onclick="exportToCSV()">
							<i class="ti-download"></i> Export to CSV
						</button>
						<button class="btn btn-info" onclick="window.print()">
							<i class="ti-printer"></i> Print Report
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
	
	<script>
		function exportToCSV() {
			// Simple CSV export functionality
			var csv = 'Staff Name,Email,Leave Type,Max Days,Used Days,Pending Days,Remaining Days\n';
			
			<?php foreach($staffStats as $staff) { ?>
				<?php foreach($staff['leave_types'] as $leaveType) { ?>
				csv += '<?php echo addslashes($staff['name']); ?>,<?php echo $staff['email']; ?>,<?php echo addslashes($leaveType['leave_type_name']); ?>,<?php echo $leaveType['max_days_per_year']; ?>,<?php echo $leaveType['used_days']; ?>,<?php echo $leaveType['pending_days']; ?>,<?php echo $leaveType['remaining_days']; ?>\n';
				<?php } ?>
			<?php } ?>
			
			var blob = new Blob([csv], { type: 'text/csv' });
			var url = window.URL.createObjectURL(blob);
			var a = document.createElement('a');
			a.setAttribute('hidden', '');
			a.setAttribute('href', url);
			a.setAttribute('download', 'leave_report_<?php echo $year; ?>.csv');
			document.body.appendChild(a);
			a.click();
			document.body.removeChild(a);
		}
	</script>
</body>
</html>
