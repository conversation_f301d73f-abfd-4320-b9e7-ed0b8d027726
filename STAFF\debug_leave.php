<?php
// Debug script for Leave functionality
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Leave System Debug</h2>";
echo "<hr>";

// Test 1: Include User class
echo "<h3>1. Testing User Class</h3>";
try {
    include('class/User.php');
    echo "✓ User class included successfully<br>";
} catch (Exception $e) {
    echo "✗ Error including User class: " . $e->getMessage() . "<br>";
    exit();
}

// Test 2: Create User object
echo "<h3>2. Testing User Object Creation</h3>";
try {
    $user = new User();
    echo "✓ User object created successfully<br>";
} catch (Exception $e) {
    echo "✗ Error creating User object: " . $e->getMessage() . "<br>";
    exit();
}

// Test 3: Check session
echo "<h3>3. Testing Session</h3>";
session_start();
if (isset($_SESSION['userid'])) {
    echo "✓ User session active (User ID: " . $_SESSION['userid'] . ")<br>";
} else {
    echo "⚠ No active user session<br>";
    echo "Please log in first, then run this debug script<br>";
}

// Test 4: Test database connection
echo "<h3>4. Testing Database Connection</h3>";
try {
    $testQuery = "SELECT 1";
    $result = mysqli_query($user->dbConnect, $testQuery);
    if ($result) {
        echo "✓ Database connection working<br>";
    } else {
        echo "✗ Database connection failed: " . mysqli_error($user->dbConnect) . "<br>";
    }
} catch (Exception $e) {
    echo "✗ Database error: " . $e->getMessage() . "<br>";
}

// Test 5: Check if tables exist
echo "<h3>5. Testing Leave Tables</h3>";
$tables = ['leave_type', 'staff_leave'];
foreach ($tables as $table) {
    $query = "SHOW TABLES LIKE '$table'";
    $result = mysqli_query($user->dbConnect, $query);
    if (mysqli_num_rows($result) > 0) {
        echo "✓ Table '$table' exists<br>";
        
        // Check data
        $countQuery = "SELECT COUNT(*) as count FROM $table";
        $countResult = mysqli_query($user->dbConnect, $countQuery);
        $countRow = mysqli_fetch_assoc($countResult);
        echo "  → Records in $table: " . $countRow['count'] . "<br>";
    } else {
        echo "✗ Table '$table' does not exist<br>";
    }
}

// Test 6: Test getLeaveTypes method
echo "<h3>6. Testing getLeaveTypes Method</h3>";
try {
    $leaveTypes = $user->getLeaveTypes();
    if (!empty($leaveTypes)) {
        echo "✓ getLeaveTypes() working - Found " . count($leaveTypes) . " leave types<br>";
        foreach ($leaveTypes as $type) {
            echo "  → " . $type['leave_type_name'] . " (Max: " . $type['max_days_per_year'] . " days)<br>";
        }
    } else {
        echo "⚠ getLeaveTypes() returned empty result<br>";
    }
} catch (Exception $e) {
    echo "✗ getLeaveTypes() error: " . $e->getMessage() . "<br>";
}

// Test 7: Test getLeaveStats method (only if user is logged in)
if (isset($_SESSION['userid'])) {
    echo "<h3>7. Testing getLeaveStats Method</h3>";
    try {
        $leaveStats = $user->getLeaveStats();
        if (!empty($leaveStats)) {
            echo "✓ getLeaveStats() working - Found " . count($leaveStats) . " leave type stats<br>";
            foreach ($leaveStats as $stat) {
                echo "  → " . $stat['leave_type_name'] . ": Used " . $stat['used_days'] . "/" . $stat['max_days_per_year'] . " days<br>";
            }
        } else {
            echo "⚠ getLeaveStats() returned empty result<br>";
        }
    } catch (Exception $e) {
        echo "✗ getLeaveStats() error: " . $e->getMessage() . "<br>";
    }
}

// Test 8: Test form submission (if POST data exists)
if (!empty($_POST)) {
    echo "<h3>8. Testing Form Submission</h3>";
    echo "POST data received:<br>";
    foreach ($_POST as $key => $value) {
        echo "  → $key: " . htmlspecialchars($value) . "<br>";
    }
    
    if (!empty($_POST["apply_leave"])) {
        echo "<br>Testing applyLeave() method:<br>";
        try {
            $message = $user->applyLeave();
            echo "✓ applyLeave() executed<br>";
            echo "Message: " . $message . "<br>";
        } catch (Exception $e) {
            echo "✗ applyLeave() error: " . $e->getMessage() . "<br>";
        }
    }
}

// Test 9: Check file permissions
echo "<h3>9. Testing File Permissions</h3>";
$uploadDir = 'upload/leave_attachments/';
if (!file_exists($uploadDir)) {
    if (mkdir($uploadDir, 0777, true)) {
        echo "✓ Created upload directory: $uploadDir<br>";
    } else {
        echo "✗ Failed to create upload directory: $uploadDir<br>";
    }
} else {
    echo "✓ Upload directory exists: $uploadDir<br>";
}

if (is_writable($uploadDir)) {
    echo "✓ Upload directory is writable<br>";
} else {
    echo "✗ Upload directory is not writable<br>";
}

echo "<hr>";
echo "<h3>Test Form</h3>";
echo "<p>Use this form to test the leave application:</p>";
?>

<form method="post" enctype="multipart/form-data" style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
    <div style="margin-bottom: 15px;">
        <label>Leave Type:</label><br>
        <select name="leave_type_id" required style="width: 100%; padding: 8px;">
            <option value="">Select Leave Type</option>
            <?php 
            if (isset($leaveTypes)) {
                foreach($leaveTypes as $type) { 
            ?>
            <option value="<?php echo $type['leave_type_id']; ?>">
                <?php echo $type['leave_type_name']; ?> (Max: <?php echo $type['max_days_per_year']; ?> days/year)
            </option>
            <?php 
                }
            }
            ?>
        </select>
    </div>
    
    <div style="margin-bottom: 15px;">
        <label>Start Date:</label><br>
        <input type="date" name="start_date" required style="width: 100%; padding: 8px;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label>End Date:</label><br>
        <input type="date" name="end_date" required style="width: 100%; padding: 8px;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label>Reason:</label><br>
        <textarea name="reason" required style="width: 100%; padding: 8px; height: 80px;" placeholder="Enter reason for leave"></textarea>
    </div>
    
    <div style="margin-bottom: 15px;">
        <label>Emergency Contact:</label><br>
        <input type="text" name="emergency_contact" style="width: 100%; padding: 8px;" placeholder="Optional">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label>Emergency Phone:</label><br>
        <input type="text" name="emergency_phone" style="width: 100%; padding: 8px;" placeholder="Optional">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label>Attachment:</label><br>
        <input type="file" name="attachment" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" style="width: 100%; padding: 8px;">
    </div>
    
    <button type="submit" name="apply_leave" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px;">
        Submit Test Application
    </button>
</form>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #495057; }
hr { border: 1px solid #dee2e6; }
</style>
