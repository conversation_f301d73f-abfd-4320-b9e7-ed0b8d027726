<?php 
include('class/User.php');
$user = new User();
$user->loginStatus();

$leaveStats = $user->getLeaveStats();
$recentLeaves = array_slice($user->getStaffLeaves(), 0, 5); // Get last 5 applications
?>
<!DOCTYPE html>
<html>
<head>
	<title>Leave Dashboard - Staff Portal</title>
	<?php include('include/header.php'); ?>
	<style>
		.dashboard-card {
			background: #fff;
			border: 1px solid #dee2e6;
			border-radius: 8px;
			padding: 20px;
			margin-bottom: 20px;
			box-shadow: 0 2px 4px rgba(0,0,0,0.1);
		}
		.card-header {
			font-size: 18px;
			font-weight: bold;
			color: #495057;
			margin-bottom: 15px;
			border-bottom: 2px solid #007bff;
			padding-bottom: 10px;
		}
		.stats-grid {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
			gap: 15px;
		}
		.stat-item {
			background: #f8f9fa;
			padding: 15px;
			border-radius: 6px;
			text-align: center;
		}
		.stat-number {
			font-size: 24px;
			font-weight: bold;
			color: #007bff;
		}
		.stat-label {
			font-size: 14px;
			color: #6c757d;
			margin-top: 5px;
		}
		.quick-actions {
			display: flex;
			gap: 10px;
			flex-wrap: wrap;
		}
		.action-btn {
			flex: 1;
			min-width: 150px;
		}
		.recent-leave {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 10px;
			border-bottom: 1px solid #dee2e6;
		}
		.recent-leave:last-child {
			border-bottom: none;
		}
		.leave-info {
			flex: 1;
		}
		.leave-status {
			font-weight: bold;
			padding: 4px 8px;
			border-radius: 4px;
			font-size: 12px;
		}
		.status-pending { background: #fff3cd; color: #856404; }
		.status-approved { background: #d4edda; color: #155724; }
		.status-rejected { background: #f8d7da; color: #721c24; }
		.welcome-section {
			background: linear-gradient(135deg, #007bff, #0056b3);
			color: white;
			padding: 30px;
			border-radius: 8px;
			margin-bottom: 20px;
			text-align: center;
		}
	</style>
</head>
<body>
	<?php include('include/container.php');?>
	<div class="container contact">
		<h2>Staff Leave Management System - Leave Dashboard</h2>

		<?php include('staffmenu.php'); ?>
		<div class="col-lg-10 col-md-10 col-sm-9 col-xs-12">
			
			<div class="welcome-section">
				<h2><i class="fa fa-calendar"></i> Leave Management Dashboard</h2>
				<p>Manage your leave applications and track your leave balance</p>
			</div>
			
			<div class="dashboard-card">
				<div class="card-header">
					<i class="fa fa-bolt"></i> Quick Actions
				</div>
				<div class="quick-actions">
					<a href="apply_leave.php" class="btn btn-primary action-btn">
						<i class="fa fa-plus"></i> Apply for Leave
					</a>
					<a href="my_leaves.php" class="btn btn-info action-btn">
						<i class="fa fa-list"></i> View My Applications
					</a>
				</div>
			</div>
			
			<div class="dashboard-card">
				<div class="card-header">
					<i class="fa fa-pie-chart"></i> Leave Balance Overview (<?php echo date('Y'); ?>)
				</div>
				<div class="stats-grid">
					<?php foreach($leaveStats as $stat) { ?>
					<div class="stat-item">
						<div class="stat-number"><?php echo $stat['remaining_days']; ?></div>
						<div class="stat-label"><?php echo $stat['leave_type_name']; ?><br>Remaining</div>
						<div style="font-size: 12px; color: #6c757d; margin-top: 5px;">
							Used: <?php echo $stat['used_days']; ?> | Pending: <?php echo $stat['pending_days']; ?>
						</div>
					</div>
					<?php } ?>
				</div>
			</div>
			
			<div class="row">
				<div class="col-md-8">
					<div class="dashboard-card">
						<div class="card-header">
							<i class="fa fa-history"></i> Recent Applications
						</div>
						<?php if(empty($recentLeaves)) { ?>
							<div class="alert alert-info">
								<i class="fa fa-info-circle"></i> No leave applications found.
								<a href="apply_leave.php" class="btn btn-primary btn-sm pull-right">Apply Now</a>
							</div>
						<?php } else { ?>
							<?php foreach($recentLeaves as $leave) { 
								$statusClass = 'status-' . $leave['status'];
							?>
							<div class="recent-leave">
								<div class="leave-info">
									<strong><?php echo $leave['leave_type_name']; ?></strong><br>
									<small class="text-muted">
										<?php echo date('M d', strtotime($leave['start_date'])); ?> - 
										<?php echo date('M d, Y', strtotime($leave['end_date'])); ?>
										(<?php echo $leave['total_days']; ?> day<?php echo $leave['total_days'] > 1 ? 's' : ''; ?>)
									</small>
								</div>
								<div>
									<span class="leave-status <?php echo $statusClass; ?>">
										<?php echo ucfirst($leave['status']); ?>
									</span>
								</div>
							</div>
							<?php } ?>
							<div style="text-align: center; margin-top: 15px;">
								<a href="my_leaves.php" class="btn btn-outline-primary btn-sm">
									View All Applications
								</a>
							</div>
						<?php } ?>
					</div>
				</div>
				
				<div class="col-md-4">
					<div class="dashboard-card">
						<div class="card-header">
							<i class="fa fa-info-circle"></i> Leave Policies
						</div>
						<div style="font-size: 14px; line-height: 1.6;">
							<p><strong>Important Notes:</strong></p>
							<ul style="padding-left: 20px;">
								<li>Submit leave applications at least 3 days in advance</li>
								<li>Medical certificates required for sick leave > 3 days</li>
								<li>Annual leave must be approved by your supervisor</li>
								<li>Emergency leave requires immediate notification</li>
							</ul>
							<p><strong>Contact HR:</strong><br>
							Email: <EMAIL><br>
							Phone: 012-3537611</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</body>
</html>
