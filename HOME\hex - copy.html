<html xmlns:v="urn:schemas-microsoft-com:vml"
xmlns:o="urn:schemas-microsoft-com:office:office"
xmlns:w="urn:schemas-microsoft-com:office:word"
xmlns:m="http://schemas.microsoft.com/office/2004/12/omml"
xmlns="http://www.w3.org/TR/REC-html40">

<head>
<meta http-equiv=Content-Type content="text/html; charset=windows-1252">
<meta name=ProgId content=Word.Document>
<meta name=Generator content="Microsoft Word 15">
<meta name=Originator content="Microsoft Word 15">
<link rel=File-List href="Hex_files/filelist.xml">
<link rel=Edit-Time-Data href="Hex_files/editdata.mso">
<!--[if !mso]>
<style>
v\:* {behavior:url(#default#VML);}
o\:* {behavior:url(#default#VML);}
w\:* {behavior:url(#default#VML);}
.shape {behavior:url(#default#VML);}
</style>
<![endif]--><!--[if gte mso 9]><xml>
 <o:DocumentProperties>
  <o:Author>Richard</o:Author>
  <o:LastAuthor>Richard</o:LastAuthor>
  <o:Revision>2</o:Revision>
  <o:TotalTime>12</o:TotalTime>
  <o:Created>2020-06-20T19:09:00Z</o:Created>
  <o:LastSaved>2020-06-20T19:09:00Z</o:LastSaved>
  <o:Pages>1</o:Pages>
  <o:Characters>1</o:Characters>
  <o:Lines>1</o:Lines>
  <o:Paragraphs>1</o:Paragraphs>
  <o:CharactersWithSpaces>1</o:CharactersWithSpaces>
  <o:Version>16.00</o:Version>
 </o:DocumentProperties>
 <o:OfficeDocumentSettings>
  <o:AllowPNG/>
 </o:OfficeDocumentSettings>
</xml><![endif]-->
<link rel=themeData href="Hex_files/themedata.thmx">
<link rel=colorSchemeMapping href="Hex_files/colorschememapping.xml">
<!--[if gte mso 9]><xml>
 <w:WordDocument>
  <w:GrammarState>Clean</w:GrammarState>
  <w:TrackMoves>false</w:TrackMoves>
  <w:TrackFormatting/>
  <w:PunctuationKerning/>
  <w:ValidateAgainstSchemas/>
  <w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid>
  <w:IgnoreMixedContent>false</w:IgnoreMixedContent>
  <w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText>
  <w:DoNotPromoteQF/>
  <w:LidThemeOther>EN-US</w:LidThemeOther>
  <w:LidThemeAsian>ZH-CN</w:LidThemeAsian>
  <w:LidThemeComplexScript>X-NONE</w:LidThemeComplexScript>
  <w:Compatibility>
   <w:BreakWrappedTables/>
   <w:SnapToGridInCell/>
   <w:WrapTextWithPunct/>
   <w:UseAsianBreakRules/>
   <w:DontGrowAutofit/>
   <w:SplitPgBreakAndParaMark/>
   <w:EnableOpenTypeKerning/>
   <w:DontFlipMirrorIndents/>
   <w:OverrideTableStyleHps/>
   <w:UseFELayout/>
  </w:Compatibility>
  <m:mathPr>
   <m:mathFont m:val="Cambria Math"/>
   <m:brkBin m:val="before"/>
   <m:brkBinSub m:val="&#45;-"/>
   <m:smallFrac m:val="off"/>
   <m:dispDef/>
   <m:lMargin m:val="0"/>
   <m:rMargin m:val="0"/>
   <m:defJc m:val="centerGroup"/>
   <m:wrapIndent m:val="1440"/>
   <m:intLim m:val="subSup"/>
   <m:naryLim m:val="undOvr"/>
  </m:mathPr></w:WordDocument>
</xml><![endif]--><!--[if gte mso 9]><xml>
 <w:LatentStyles DefLockedState="false" DefUnhideWhenUsed="false"
  DefSemiHidden="false" DefQFormat="false" DefPriority="99"
  LatentStyleCount="376">
  <w:LsdException Locked="false" Priority="0" QFormat="true" Name="Normal"/>
  <w:LsdException Locked="false" Priority="9" QFormat="true" Name="heading 1"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 2"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 3"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 4"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 5"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 6"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 7"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 8"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 9"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 6"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 7"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 8"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 9"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 1"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 2"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 3"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 4"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 5"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 6"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 7"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 8"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 9"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Normal Indent"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="footnote text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="annotation text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="header"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="footer"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index heading"/>
  <w:LsdException Locked="false" Priority="35" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="caption"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="table of figures"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="envelope address"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="envelope return"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="footnote reference"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="annotation reference"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="line number"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="page number"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="endnote reference"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="endnote text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="table of authorities"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="macro"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="toa heading"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Bullet"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Number"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Bullet 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Bullet 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Bullet 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Bullet 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Number 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Number 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Number 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Number 5"/>
  <w:LsdException Locked="false" Priority="10" QFormat="true" Name="Title"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Closing"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Signature"/>
  <w:LsdException Locked="false" Priority="1" SemiHidden="true"
   UnhideWhenUsed="true" Name="Default Paragraph Font"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text Indent"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Continue"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Continue 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Continue 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Continue 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Continue 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Message Header"/>
  <w:LsdException Locked="false" Priority="11" QFormat="true" Name="Subtitle"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Salutation"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Date"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text First Indent"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text First Indent 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Note Heading"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text Indent 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text Indent 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Block Text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Hyperlink"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="FollowedHyperlink"/>
  <w:LsdException Locked="false" Priority="22" QFormat="true" Name="Strong"/>
  <w:LsdException Locked="false" Priority="20" QFormat="true" Name="Emphasis"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Document Map"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Plain Text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="E-mail Signature"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Top of Form"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Bottom of Form"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Normal (Web)"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Acronym"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Address"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Cite"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Code"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Definition"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Keyboard"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Preformatted"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Sample"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Typewriter"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Variable"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Normal Table"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="annotation subject"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="No List"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Outline List 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Outline List 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Outline List 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Simple 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Simple 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Simple 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Classic 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Classic 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Classic 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Classic 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Colorful 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Colorful 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Colorful 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 6"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 7"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 8"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 6"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 7"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 8"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table 3D effects 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table 3D effects 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table 3D effects 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Contemporary"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Elegant"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Professional"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Subtle 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Subtle 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Web 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Web 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Web 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Balloon Text"/>
  <w:LsdException Locked="false" Priority="59" Name="Table Grid"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Theme"/>
  <w:LsdException Locked="false" SemiHidden="true" Name="Placeholder Text"/>
  <w:LsdException Locked="false" Priority="1" QFormat="true" Name="No Spacing"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 1"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 1"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 1"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 1"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 1"/>
  <w:LsdException Locked="false" SemiHidden="true" Name="Revision"/>
  <w:LsdException Locked="false" Priority="34" QFormat="true"
   Name="List Paragraph"/>
  <w:LsdException Locked="false" Priority="29" QFormat="true" Name="Quote"/>
  <w:LsdException Locked="false" Priority="30" QFormat="true"
   Name="Intense Quote"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 1"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 1"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 1"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 1"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 1"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 1"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 2"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 2"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 2"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 2"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 2"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 2"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 2"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 2"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 2"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 2"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 2"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 3"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 3"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 3"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 3"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 3"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 3"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 3"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 3"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 3"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 3"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 3"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 4"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 4"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 4"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 4"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 4"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 4"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 4"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 4"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 4"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 4"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 4"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 5"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 5"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 5"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 5"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 5"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 5"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 5"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 5"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 5"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 5"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 5"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 6"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 6"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 6"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 6"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 6"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 6"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 6"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 6"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 6"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 6"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 6"/>
  <w:LsdException Locked="false" Priority="19" QFormat="true"
   Name="Subtle Emphasis"/>
  <w:LsdException Locked="false" Priority="21" QFormat="true"
   Name="Intense Emphasis"/>
  <w:LsdException Locked="false" Priority="31" QFormat="true"
   Name="Subtle Reference"/>
  <w:LsdException Locked="false" Priority="32" QFormat="true"
   Name="Intense Reference"/>
  <w:LsdException Locked="false" Priority="33" QFormat="true" Name="Book Title"/>
  <w:LsdException Locked="false" Priority="37" SemiHidden="true"
   UnhideWhenUsed="true" Name="Bibliography"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="TOC Heading"/>
  <w:LsdException Locked="false" Priority="41" Name="Plain Table 1"/>
  <w:LsdException Locked="false" Priority="42" Name="Plain Table 2"/>
  <w:LsdException Locked="false" Priority="43" Name="Plain Table 3"/>
  <w:LsdException Locked="false" Priority="44" Name="Plain Table 4"/>
  <w:LsdException Locked="false" Priority="45" Name="Plain Table 5"/>
  <w:LsdException Locked="false" Priority="40" Name="Grid Table Light"/>
  <w:LsdException Locked="false" Priority="46" Name="Grid Table 1 Light"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark"/>
  <w:LsdException Locked="false" Priority="51" Name="Grid Table 6 Colorful"/>
  <w:LsdException Locked="false" Priority="52" Name="Grid Table 7 Colorful"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 1"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 1"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 1"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 1"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 2"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 2"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 2"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 2"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 3"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 3"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 3"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 3"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 4"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 4"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 4"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 4"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 5"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 5"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 5"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 5"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 6"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 6"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 6"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 6"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 6"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 6"/>
  <w:LsdException Locked="false" Priority="46" Name="List Table 1 Light"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark"/>
  <w:LsdException Locked="false" Priority="51" Name="List Table 6 Colorful"/>
  <w:LsdException Locked="false" Priority="52" Name="List Table 7 Colorful"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 1"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 1"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 1"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 1"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 2"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 2"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 2"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 2"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 3"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 3"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 3"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 3"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 4"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 4"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 4"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 4"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 5"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 5"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 5"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 5"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 6"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 6"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 6"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 6"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 6"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 6"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Mention"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Smart Hyperlink"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Hashtag"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Unresolved Mention"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Smart Link"/>
 </w:LatentStyles>
</xml><![endif]-->
<style>
<!--
 /* Font Definitions */
 @font-face
	{font-family:SimSun;
	panose-1:2 1 6 0 3 1 1 1 1 1;
	mso-font-alt:\5B8B\4F53;
	mso-font-charset:134;
	mso-generic-font-family:auto;
	mso-font-pitch:variable;
	mso-font-signature:3 680460288 22 0 262145 0;}
@font-face
	{font-family:"Cambria Math";
	panose-1:2 4 5 3 5 4 6 3 2 4;
	mso-font-charset:0;
	mso-generic-font-family:roman;
	mso-font-pitch:variable;
	mso-font-signature:3 0 0 0 1 0;}
@font-face
	{font-family:Calibri;
	panose-1:2 15 5 2 2 2 4 3 2 4;
	mso-font-charset:0;
	mso-generic-font-family:swiss;
	mso-font-pitch:variable;
	mso-font-signature:-469750017 -1073732485 9 0 511 0;}
@font-face
	{font-family:"\@SimSun";
	panose-1:2 1 6 0 3 1 1 1 1 1;
	mso-font-charset:134;
	mso-generic-font-family:auto;
	mso-font-pitch:variable;
	mso-font-signature:3 680460288 22 0 262145 0;}
 /* Style Definitions */
 p.MsoNormal, li.MsoNormal, div.MsoNormal
	{mso-style-unhide:no;
	mso-style-qformat:yes;
	mso-style-parent:"";
	margin-top:0cm;
	margin-right:0cm;
	margin-bottom:10.0pt;
	margin-left:0cm;
	line-height:115%;
	mso-pagination:widow-orphan;
	font-size:11.0pt;
	font-family:"Calibri",sans-serif;
	mso-ascii-font-family:Calibri;
	mso-ascii-theme-font:minor-latin;
	mso-fareast-font-family:SimSun;
	mso-fareast-theme-font:minor-fareast;
	mso-hansi-font-family:Calibri;
	mso-hansi-theme-font:minor-latin;
	mso-bidi-font-family:"Times New Roman";
	mso-bidi-theme-font:minor-bidi;}
p.MsoHeader, li.MsoHeader, div.MsoHeader
	{mso-style-priority:99;
	mso-style-link:"Header Char";
	margin:0cm;
	margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	tab-stops:center 234.0pt right 468.0pt;
	font-size:11.0pt;
	font-family:"Calibri",sans-serif;
	mso-ascii-font-family:Calibri;
	mso-ascii-theme-font:minor-latin;
	mso-fareast-font-family:SimSun;
	mso-fareast-theme-font:minor-fareast;
	mso-hansi-font-family:Calibri;
	mso-hansi-theme-font:minor-latin;
	mso-bidi-font-family:"Times New Roman";
	mso-bidi-theme-font:minor-bidi;}
p.MsoFooter, li.MsoFooter, div.MsoFooter
	{mso-style-priority:99;
	mso-style-link:"Footer Char";
	margin:0cm;
	margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	tab-stops:center 234.0pt right 468.0pt;
	font-size:11.0pt;
	font-family:"Calibri",sans-serif;
	mso-ascii-font-family:Calibri;
	mso-ascii-theme-font:minor-latin;
	mso-fareast-font-family:SimSun;
	mso-fareast-theme-font:minor-fareast;
	mso-hansi-font-family:Calibri;
	mso-hansi-theme-font:minor-latin;
	mso-bidi-font-family:"Times New Roman";
	mso-bidi-theme-font:minor-bidi;}
span.HeaderChar
	{mso-style-name:"Header Char";
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-locked:yes;
	mso-style-link:Header;}
span.FooterChar
	{mso-style-name:"Footer Char";
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-locked:yes;
	mso-style-link:Footer;}
.MsoChpDefault
	{mso-style-type:export-only;
	mso-default-props:yes;
	font-family:"Calibri",sans-serif;
	mso-ascii-font-family:Calibri;
	mso-ascii-theme-font:minor-latin;
	mso-fareast-font-family:SimSun;
	mso-fareast-theme-font:minor-fareast;
	mso-hansi-font-family:Calibri;
	mso-hansi-theme-font:minor-latin;
	mso-bidi-font-family:"Times New Roman";
	mso-bidi-theme-font:minor-bidi;}
.MsoPapDefault
	{mso-style-type:export-only;
	margin-bottom:10.0pt;
	line-height:115%;}
 /* Page Definitions */
 @page
	{mso-footnote-separator:url("Hex_files/header.html") fs;
	mso-footnote-continuation-separator:url("Hex_files/header.html") fcs;
	mso-endnote-separator:url("Hex_files/header.html") es;
	mso-endnote-continuation-separator:url("Hex_files/header.html") ecs;}
@page WordSection1
	{size:612.0pt 792.0pt;
	margin:72.0pt 72.0pt 72.0pt 72.0pt;
	mso-header-margin:36.0pt;
	mso-footer-margin:36.0pt;
	mso-paper-source:0;}
div.WordSection1
	{page:WordSection1;}
-->
</style>
<!--[if gte mso 10]>
<style>
 /* Style Definitions */
 table.MsoNormalTable
	{mso-style-name:"Table Normal";
	mso-tstyle-rowband-size:0;
	mso-tstyle-colband-size:0;
	mso-style-noshow:yes;
	mso-style-priority:99;
	mso-style-parent:"";
	mso-padding-alt:0cm 5.4pt 0cm 5.4pt;
	mso-para-margin-top:0cm;
	mso-para-margin-right:0cm;
	mso-para-margin-bottom:10.0pt;
	mso-para-margin-left:0cm;
	line-height:115%;
	mso-pagination:widow-orphan;
	font-size:11.0pt;
	font-family:"Calibri",sans-serif;
	mso-ascii-font-family:Calibri;
	mso-ascii-theme-font:minor-latin;
	mso-hansi-font-family:Calibri;
	mso-hansi-theme-font:minor-latin;
	mso-bidi-font-family:"Times New Roman";
	mso-bidi-theme-font:minor-bidi;}
</style>
<![endif]--><!--[if gte mso 9]><xml>
 <o:shapedefaults v:ext="edit" spidmax="1026"/>
</xml><![endif]--><!--[if gte mso 9]><xml>
 <o:shapelayout v:ext="edit">
  <o:idmap v:ext="edit" data="1"/>
 </o:shapelayout></xml><![endif]-->
</head>

<body lang=EN-US style='tab-interval:36.0pt'>

<div class=WordSection1>

<p class=MsoNormal><span style='mso-no-proof:yes'><!--[if gte vml 1]><v:shapetype
 id="_x0000_t75" coordsize="21600,21600" o:spt="75" o:preferrelative="t"
 path="m@4@5l@4@11@9@11@9@5xe" filled="f" stroked="f">
 <v:stroke joinstyle="miter"/>
 <v:formulas>
  <v:f eqn="if lineDrawn pixelLineWidth 0"/>
  <v:f eqn="sum @0 1 0"/>
  <v:f eqn="sum 0 0 @1"/>
  <v:f eqn="prod @2 1 2"/>
  <v:f eqn="prod @3 21600 pixelWidth"/>
  <v:f eqn="prod @3 21600 pixelHeight"/>
  <v:f eqn="sum @0 0 1"/>
  <v:f eqn="prod @6 1 2"/>
  <v:f eqn="prod @7 21600 pixelWidth"/>
  <v:f eqn="sum @8 21600 0"/>
  <v:f eqn="prod @7 21600 pixelHeight"/>
  <v:f eqn="sum @10 21600 0"/>
 </v:formulas>
 <v:path o:extrusionok="f" gradientshapeok="t" o:connecttype="rect"/>
 <o:lock v:ext="edit" aspectratio="t"/>
</v:shapetype><v:shape id="Diagram_x0020_1" o:spid="_x0000_i1025" type="#_x0000_t75"
 style='width:7in;height:320.25pt;visibility:visible' o:gfxdata="UEsDBBQABgAIAAAAIQBgJjcoXAEAAHYEAAATAAAAW0NvbnRlbnRfVHlwZXNdLnhtbLSUy07DMBRE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">
 <v:imagedata src="Hex_files/image001.png" o:title="" cropleft="-21295f"
  cropright="-21199f"/>
 <o:lock v:ext="edit" aspectratio="f"/>
</v:shape><![endif]--><![if !vml]><img width=672 height=427
src="Hex_files/image002.png" v:shapes="Diagram_x0020_1"><![endif]></span></p>

</div>

</body>

</html>
