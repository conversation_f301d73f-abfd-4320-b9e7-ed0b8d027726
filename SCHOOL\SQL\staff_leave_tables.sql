-- Staff Leave Management Tables
-- Created for SIS 1.01 Staff Leave Module

USE `sms101`;

--
-- Table structure for table `leave_type`
--

DROP TABLE IF EXISTS `leave_type`;
CREATE TABLE `leave_type` (
  `leave_type_id` int NOT NULL AUTO_INCREMENT,
  `leave_type_name` varchar(50) NOT NULL,
  `max_days_per_year` int DEFAULT NULL,
  `description` text,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`leave_type_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `leave_type`
--

INSERT INTO `leave_type` VALUES 
(1,'Annual Leave',14,'Annual vacation leave',1,NOW()),
(2,'Sick Leave',14,'Medical leave for illness',1,NOW()),
(3,'Emergency Leave',7,'Emergency personal leave',1,NOW()),
(4,'Maternity Leave',90,'Maternity leave for female staff',1,NOW()),
(5,'Paternity Leave',7,'Paternity leave for male staff',1,NOW());

--
-- Table structure for table `staff_leave`
--

DROP TABLE IF EXISTS `staff_leave`;
CREATE TABLE `staff_leave` (
  `leave_id` int NOT NULL AUTO_INCREMENT,
  `staff_id` int NOT NULL,
  `leave_type_id` int NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `total_days` int NOT NULL,
  `reason` text NOT NULL,
  `status` enum('pending','approved','rejected','cancelled') DEFAULT 'pending',
  `applied_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `approved_by` int DEFAULT NULL,
  `approved_date` timestamp NULL DEFAULT NULL,
  `admin_comments` text,
  `emergency_contact` varchar(100) DEFAULT NULL,
  `emergency_phone` varchar(20) DEFAULT NULL,
  `attachment` varchar(255) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`leave_id`),
  KEY `fk_staff_leave_staff` (`staff_id`),
  KEY `fk_staff_leave_type` (`leave_type_id`),
  KEY `fk_staff_leave_approver` (`approved_by`),
  CONSTRAINT `fk_staff_leave_staff` FOREIGN KEY (`staff_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_staff_leave_type` FOREIGN KEY (`leave_type_id`) REFERENCES `leave_type` (`leave_type_id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_staff_leave_approver` FOREIGN KEY (`approved_by`) REFERENCES `user` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Create indexes for better performance
CREATE INDEX idx_staff_leave_status ON staff_leave(status);
CREATE INDEX idx_staff_leave_dates ON staff_leave(start_date, end_date);
CREATE INDEX idx_staff_leave_applied_date ON staff_leave(applied_date);
