# Staff Leave:
在原有sis的项目里去扩展这项模块 这会扩展关于到 Teachers, Staff, School

## Students:
Students:
| 功能      | 描述                      |
| ------- | ----------------------- |
| 浏览图书目录  | 查看所有图书信息（分类、作者、库存等）     |
| 借书申请    | 提交借阅请求，或在有库存时直接借阅       |
| 归还图书    | 发起归还流程，或查看归还状态          |
| 借阅历史    | 查看自己借阅过的书籍和状态（借出、归还、逾期） |
| 预约图书    | 如果图书被借出，可预约排队           |
| 通知提醒    | 收到还书提醒、预约通知等            |

## Teachers:
| 功能       | 描述                      |
| -------- | ----------------------- |
| 所有学生功能   | 教师具备学生的所有图书借阅功能         |
| 推荐图书     | 推荐书籍给班级/课程学生阅读          |
| 班级图书借阅情况 | 查看所教班级学生借阅数据（选读书籍、归还状态） |
| 图书需求申请   | 提交新书采购建议或教学书目补充         |
| 借阅期限申请   | 申请延长教学用书借阅时间（如研究、课程阅读）  |

## Staff:
| 功能       | 描述                      |
| -------- | ----------------------- |
| 图书管理     | 添加、编辑、删除图书信息（标题、分类、库存等） |
| 借阅处理     | 处理借书与还书事务（审批、登记等）       |
| 预约队列管理   | 管理图书预约队列、分配图书           |
| 库存管理     | 库存盘点、损毁/遗失图书处理          |
| 借阅记录查看   | 查看所有借阅记录，按用户筛选          |
| 逾期管理     | 发送提醒、生成罚款记录、冻结账户等       |
| 图书采购建议处理 | 查看教师/学生提交的图书采购申请并处理     |

## School:
| 功能          | 描述                      |
| ----------- | ----------------------- |
| 所有图书馆系统数据总览 | 总体数据（图书数量、分类、借阅量） |
| 权限管理        | 设置不同角色的访问权限（学生、教师、职员等）  |
| 系统设置        | 配置借书天数、逾期罚款规则、通知方式等     |
| 报表导出        | 导出借阅数据、使用情况报表（供学校决策）    |

# 数据库设计
library 的数据库设计是要跟原有的sms101有关联的
sms101:
C:\shared\SIS1.01\dumps\Dump20240104.sql

# 项目结构
Teachers: 后端功能写在 TEACHERS/class/User.php
Staff: 后端功能写在 STAFF/class/User.php
School: 后端功能写在 SCHOOL/class/School.php