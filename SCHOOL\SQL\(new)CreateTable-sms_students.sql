CREATE TABLE `sms_students` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(40) NOT NULL,
  `gender` varchar(40) NOT NULL,
  `dob` varchar(255) DEFAULT NULL,
  `photo` varchar(40) DEFAULT NULL,
  `mobile` varchar(20) DEFAULT NULL,
  `email` varchar(40) DEFAULT NULL,
  `current_address` varchar(40) DEFAULT NULL,
  `permanent_address` varchar(40) DEFAULT NULL,
  `father_name` varchar(255) DEFAULT NULL,
  `father_mobile` int unsigned DEFAULT NULL,
  `father_occupation` varchar(255) DEFAULT NULL,
  `mother_name` varchar(255) DEFAULT NULL,
  `mother_mobile` int unsigned DEFAULT NULL,
  `admission_no` varchar(20) DEFAULT NULL,
  `roll_no` int DEFAULT NULL,
  `class` int unsigned NOT NULL,
  `section` int NOT NULL,
  `stream` varchar(50) DEFAULT NULL,
  `hostel` varchar(255) DEFAULT NULL,
  `admission_date` varchar(255) DEFAULT NULL,
  `category` varchar(20) DEFAULT NULL,
  `academic_year` int unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8;
