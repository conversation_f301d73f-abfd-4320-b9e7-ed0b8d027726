# Staff Leave Management System

A comprehensive leave management system integrated into the existing SIS (School Information System) for managing staff leave applications, approvals, and reporting.

## Features

### For Staff Members
- **Leave Dashboard**: Overview of leave balances and recent applications
- **Apply for Leave**: Submit leave applications with supporting documents
- **My Applications**: View status and history of leave applications
- **Leave Balance Tracking**: Real-time tracking of used, pending, and remaining leave days
- **Multiple Leave Types**: Support for Annual, Sick, Emergency, Maternity, and Paternity leave

### For Administrators
- **Application Management**: Review, approve, or reject leave applications
- **Detailed Application View**: Complete application details with staff information
- **Leave Reports**: Comprehensive reporting and statistics
- **Staff Leave Statistics**: Overview of all staff leave usage
- **Export Functionality**: Export data to CSV format
- **Filter and Search**: Filter applications by status, date range, or staff member

## Installation

### 1. Database Setup

Run the SQL script to create the required tables:

```sql
-- Execute the following file in your MySQL database
SCHOOL/SQL/staff_leave_tables.sql
```

This will create:
- `leave_type` table with predefined leave types
- `staff_leave` table for storing leave applications
- Proper foreign key relationships and indexes

### 2. File Structure

The system adds the following files to your existing SIS structure:

```
STAFF/
├── apply_leave.php          # Leave application form
├── my_leaves.php           # Staff leave history
├── leave_dashboard.php     # Leave overview dashboard
├── test_leave_system.php   # System testing script
├── CSS/
│   └── leave-management.css # Staff leave styles
└── JS/
    └── leave-management.js  # Staff leave JavaScript

SCHOOL/
├── staff_leave.php         # Admin leave management
├── leave_details.php       # Detailed application view
├── leave_reports.php       # Leave reports and statistics
├── test_leave_admin.php    # Admin testing script
├── CSS/
│   └── leave-admin.css     # Admin leave styles
├── JS/
│   └── leave-admin.js      # Admin leave JavaScript
└── SQL/
    └── staff_leave_tables.sql # Database creation script
```

### 3. Configuration Updates

The system automatically updates `config.php` with new table references:
- `staff_leaveTable = 'staff_leave'`
- `leave_typeTable = 'leave_type'`

### 4. Menu Integration

Navigation menus are updated to include leave management options:

**STAFF Module Menu:**
- Leave Dashboard
- Apply for Leave
- My Applications

**SCHOOL Module Menu:**
- Staff Leave (for application management)
- Leave Reports (for statistics and reporting)

### 5. Permissions Setup

Ensure proper directory permissions for file uploads:

```bash
chmod 755 STAFF/upload/leave_attachments/
```

## Usage

### For Staff Members

1. **Access the System**
   - Log in to the STAFF module
   - Navigate to "Leave Management" in the menu

2. **Apply for Leave**
   - Click "Apply for Leave"
   - Select leave type and dates
   - Provide reason and emergency contact
   - Upload supporting documents (optional)
   - Submit application

3. **Track Applications**
   - View "My Applications" to see status
   - Check "Leave Dashboard" for balance overview

### For Administrators

1. **Access Admin Panel**
   - Log in to the SCHOOL module as an administrator
   - Navigate to "Staff Leave" in the side menu

2. **Review Applications**
   - View all pending applications
   - Click "View Details" for complete information
   - Approve or reject with comments

3. **Generate Reports**
   - Go to "Leave Reports"
   - Select year for statistics
   - Export data as needed

## Testing

### Staff Module Testing

Run the test script to verify staff functionality:

```
http://yoursite.com/STAFF/test_leave_system.php
```

### Admin Module Testing

Run the admin test script to verify administrative functionality:

```
http://yoursite.com/SCHOOL/test_leave_admin.php
```

## Leave Types

The system comes with predefined leave types:

| Leave Type | Max Days/Year | Description |
|------------|---------------|-------------|
| Annual Leave | 14 | Annual vacation leave |
| Sick Leave | 14 | Medical leave for illness |
| Emergency Leave | 7 | Emergency personal leave |
| Maternity Leave | 90 | Maternity leave for female staff |
| Paternity Leave | 7 | Paternity leave for male staff |

## User Roles and Permissions

### Staff Members (designation: 2, 3)
- Can apply for leave
- Can view their own applications
- Can track leave balances
- Can upload supporting documents

### Administrators (designation: 1)
- Can view all leave applications
- Can approve/reject applications
- Can add comments to applications
- Can generate reports
- Can export data

### Teachers (designation: 3)
- Same permissions as staff members
- Can access through STAFF module

## Technical Details

### Database Schema

**leave_type Table:**
- `leave_type_id` (Primary Key)
- `leave_type_name`
- `max_days_per_year`
- `description`
- `is_active`
- `created_at`

**staff_leave Table:**
- `leave_id` (Primary Key)
- `staff_id` (Foreign Key to user.id)
- `leave_type_id` (Foreign Key to leave_type.leave_type_id)
- `start_date`, `end_date`, `total_days`
- `reason`, `status`
- `applied_date`, `approved_by`, `approved_date`
- `admin_comments`
- `emergency_contact`, `emergency_phone`
- `attachment`
- `created_at`, `updated_at`

### File Upload Support

Supported file types for attachments:
- PDF (.pdf)
- Word Documents (.doc, .docx)
- Images (.jpg, .jpeg, .png)
- Maximum file size: 5MB

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Verify database credentials in config.php
   - Ensure tables are created properly

2. **File Upload Issues**
   - Check directory permissions
   - Verify file size limits
   - Ensure supported file types

3. **Menu Not Showing**
   - Clear browser cache
   - Verify menu.php and side-menu.php updates

4. **Permission Denied**
   - Check user designation values
   - Verify session variables

### Support

For technical support or customization requests, please contact the development team.

## Version History

- **v1.0** - Initial release with core functionality
- **v1.1** - Added reporting and export features
- **v1.2** - Enhanced UI and mobile responsiveness

## License

This system is part of the SIS (School Information System) and follows the same licensing terms.
