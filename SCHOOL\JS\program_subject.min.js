'use strict';$(document).ready(function(){var c=$("#program_subjectList").DataTable({lengthChange:!1,processing:!0,serverSide:!0,order:[],ajax:{url:"action.php",type:"POST",data:{action:"listProgram_Subject"},dataType:"json"},columnDefs:[{targets:[0,-2,-1],orderable:!1}],pageLength:10});$("#addProgram_Subject").click(function(){$("#program_subjectModal").modal("show");$("#program_subjectForm")[0].reset();$(".modal-title").html("<i class='fa fa-plus'></i> Add and assign Subject to Program");$("#action").val("addProgram_Subject");
$("#save").val("Save")});$(document).on("submit","#program_subjectForm",function(a){a.preventDefault();$("#save").attr("disabled","disabled");a=$(this).serialize();$.ajax({url:"action.php",method:"POST",data:a,success:function(b){$("#program_subjectForm")[0].reset();$("#program_subjectModal").modal("hide");$("#save").attr("disabled",!1);c.ajax.reload()}})});$(document).on("click",".update",function(){var a=$(this).attr("id").split("_");document.getElementById("pid").value=a[0];document.getElementById("sid").value=
a[1];$.ajax({url:"action.php",method:"POST",data:{program_id:a[0],subject_id:a[1],action:"getProgram_SubjectDetails"},dataType:"json",success:function(b){$("#program_subjectModal").modal("show");$("#program_id").val(b.program_id);$("#subject_id").val(b.subject_id);$(".modal-title").html("<i class='fa fa-plus'></i> Update Program's assigned Subject");$("#action").val("updateProgram_Subject");$("#save").val("Save")}})});$(document).on("click",".delete",function(){var a=$(this).attr("id").split("_");
if(confirm("Are you sure you want to remove this Program's assigned Subject?"))$.ajax({url:"action.php",method:"POST",data:{program_id:a[0],subject_id:a[1],action:"deleteProgram_Subject"},success:function(b){c.ajax.reload()}});else return!1})});