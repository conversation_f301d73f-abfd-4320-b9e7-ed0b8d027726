<?php 
include('class/School.php');
$user = new School();
$user->adminLoginStatus();

$message = '';
if(!empty($_POST["process_leave"])) {
	$message = $user->processLeaveApplication();
}

$leave_id = isset($_GET['id']) ? $_GET['id'] : 0;
$leaveDetails = $user->getLeaveDetails($leave_id);

if(!$leaveDetails) {
	header("Location: staff_leave.php");
	exit();
}
?>
<!DOCTYPE html>
<html>
<head>
	<title>Leave Application Details - School Admin</title>
	<?php include('inc/header.php'); ?>
	<?php include('include_files.php');?>
	<style>
		.status-pending { color: #ffc107; font-weight: bold; }
		.status-approved { color: #28a745; font-weight: bold; }
		.status-rejected { color: #dc3545; font-weight: bold; }
		.status-cancelled { color: #6c757d; font-weight: bold; }
		.detail-card {
			background: #fff;
			border: 1px solid #dee2e6;
			border-radius: 8px;
			padding: 20px;
			margin-bottom: 20px;
		}
		.detail-header {
			border-bottom: 2px solid #007bff;
			padding-bottom: 15px;
			margin-bottom: 20px;
		}
		.info-row {
			display: flex;
			margin-bottom: 15px;
			border-bottom: 1px solid #f8f9fa;
			padding-bottom: 10px;
		}
		.info-label {
			font-weight: bold;
			width: 150px;
			color: #495057;
		}
		.info-value {
			flex: 1;
			color: #6c757d;
		}
		.attachment-preview {
			max-width: 200px;
			max-height: 200px;
			border: 1px solid #dee2e6;
			border-radius: 4px;
		}
		.action-buttons {
			position: sticky;
			bottom: 20px;
			background: #fff;
			padding: 15px;
			border-top: 1px solid #dee2e6;
			margin-top: 20px;
		}
	</style>
</head>
<body>
	<?php include('inc/container.php');?>
	<div class="container">	
		<?php include('side-menu.php');	?>
		<div class="content">
			<div class="container-fluid">
				<div>   
					<a href="staff_leave.php"><strong><span class="ti-arrow-left"></span> Back to Leave Management</strong></a>
					<hr>
					
					<?php if($message != '') { ?>
						<div class="alert alert-info alert-dismissible">
							<button type="button" class="close" data-dismiss="alert">&times;</button>
							<?php echo $message; ?>
						</div>
					<?php } ?>
					
					<div class="detail-card">
						<div class="detail-header">
							<div style="display: flex; justify-content: space-between; align-items: center;">
								<h3><i class="ti-calendar"></i> Leave Application Details</h3>
								<span class="status-<?php echo $leaveDetails['status']; ?>" style="font-size: 18px;">
									<?php echo ucfirst($leaveDetails['status']); ?>
								</span>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-8">
								<!-- Staff Information -->
								<h4><i class="ti-user"></i> Staff Information</h4>
								<div class="info-row">
									<div class="info-label">Name:</div>
									<div class="info-value"><?php echo $leaveDetails['staff_first_name'] . ' ' . $leaveDetails['staff_last_name']; ?></div>
								</div>
								<div class="info-row">
									<div class="info-label">Email:</div>
									<div class="info-value"><?php echo $leaveDetails['staff_email']; ?></div>
								</div>
								<div class="info-row">
									<div class="info-label">Mobile:</div>
									<div class="info-value"><?php echo $leaveDetails['staff_mobile']; ?></div>
								</div>
								
								<br>
								<!-- Leave Information -->
								<h4><i class="ti-calendar"></i> Leave Information</h4>
								<div class="info-row">
									<div class="info-label">Leave Type:</div>
									<div class="info-value"><?php echo $leaveDetails['leave_type_name']; ?></div>
								</div>
								<div class="info-row">
									<div class="info-label">Start Date:</div>
									<div class="info-value"><?php echo date('l, F d, Y', strtotime($leaveDetails['start_date'])); ?></div>
								</div>
								<div class="info-row">
									<div class="info-label">End Date:</div>
									<div class="info-value"><?php echo date('l, F d, Y', strtotime($leaveDetails['end_date'])); ?></div>
								</div>
								<div class="info-row">
									<div class="info-label">Total Days:</div>
									<div class="info-value"><?php echo $leaveDetails['total_days']; ?> day<?php echo $leaveDetails['total_days'] > 1 ? 's' : ''; ?></div>
								</div>
								<div class="info-row">
									<div class="info-label">Applied Date:</div>
									<div class="info-value"><?php echo date('F d, Y g:i A', strtotime($leaveDetails['applied_date'])); ?></div>
								</div>
								
								<br>
								<!-- Reason -->
								<h4><i class="ti-comment"></i> Reason for Leave</h4>
								<div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin-bottom: 20px;">
									<?php echo nl2br(htmlspecialchars($leaveDetails['reason'])); ?>
								</div>
								
								<!-- Emergency Contact -->
								<?php if($leaveDetails['emergency_contact']) { ?>
								<h4><i class="ti-mobile"></i> Emergency Contact</h4>
								<div class="info-row">
									<div class="info-label">Contact Name:</div>
									<div class="info-value"><?php echo htmlspecialchars($leaveDetails['emergency_contact']); ?></div>
								</div>
								<?php if($leaveDetails['emergency_phone']) { ?>
								<div class="info-row">
									<div class="info-label">Contact Phone:</div>
									<div class="info-value"><?php echo htmlspecialchars($leaveDetails['emergency_phone']); ?></div>
								</div>
								<?php } ?>
								<br>
								<?php } ?>
								
								<!-- Attachment -->
								<?php if($leaveDetails['attachment']) { ?>
								<h4><i class="ti-paperclip"></i> Supporting Document</h4>
								<div style="margin-bottom: 20px;">
									<a href="../STAFF/upload/leave_attachments/<?php echo $leaveDetails['attachment']; ?>" 
									   target="_blank" class="btn btn-outline-primary">
										<i class="ti-download"></i> View/Download Document
									</a>
								</div>
								<?php } ?>
								
								<!-- Approval Information -->
								<?php if($leaveDetails['status'] != 'pending') { ?>
								<h4><i class="ti-check"></i> Approval Information</h4>
								<?php if($leaveDetails['approved_date']) { ?>
								<div class="info-row">
									<div class="info-label">Action Date:</div>
									<div class="info-value"><?php echo date('F d, Y g:i A', strtotime($leaveDetails['approved_date'])); ?></div>
								</div>
								<?php } ?>
								<?php if($leaveDetails['approver_first_name']) { ?>
								<div class="info-row">
									<div class="info-label">Processed By:</div>
									<div class="info-value"><?php echo $leaveDetails['approver_first_name'] . ' ' . $leaveDetails['approver_last_name']; ?></div>
								</div>
								<?php } ?>
								<?php if($leaveDetails['admin_comments']) { ?>
								<div class="info-row">
									<div class="info-label">Admin Comments:</div>
									<div class="info-value">
										<div style="background: #e9ecef; padding: 10px; border-radius: 4px;">
											<?php echo nl2br(htmlspecialchars($leaveDetails['admin_comments'])); ?>
										</div>
									</div>
								</div>
								<?php } ?>
								<?php } ?>
							</div>
							
							<div class="col-md-4">
								<!-- Quick Stats -->
								<div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin-bottom: 20px;">
									<h5>Application Summary</h5>
									<div style="margin: 10px 0;">
										<strong>Application ID:</strong> #<?php echo str_pad($leaveDetails['leave_id'], 4, '0', STR_PAD_LEFT); ?>
									</div>
									<div style="margin: 10px 0;">
										<strong>Status:</strong> 
										<span class="status-<?php echo $leaveDetails['status']; ?>">
											<?php echo ucfirst($leaveDetails['status']); ?>
										</span>
									</div>
									<div style="margin: 10px 0;">
										<strong>Duration:</strong> <?php echo $leaveDetails['total_days']; ?> day(s)
									</div>
								</div>
								
								<!-- Action Buttons -->
								<?php if($leaveDetails['status'] == 'pending') { ?>
								<div style="background: #fff; border: 1px solid #dee2e6; padding: 15px; border-radius: 6px;">
									<h5>Actions</h5>
									<div style="margin-top: 15px;">
										<button class="btn btn-success btn-block" 
												onclick="processLeave(<?php echo $leaveDetails['leave_id']; ?>, 'approved')">
											<i class="ti-check"></i> Approve Application
										</button>
										<button class="btn btn-danger btn-block" 
												onclick="processLeave(<?php echo $leaveDetails['leave_id']; ?>, 'rejected')">
											<i class="ti-close"></i> Reject Application
										</button>
									</div>
								</div>
								<?php } ?>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	
	<!-- Process Leave Modal -->
	<div class="modal fade" id="processLeaveModal" tabindex="-1" role="dialog">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<form method="post">
					<div class="modal-header">
						<h4 class="modal-title" id="modalTitle">Process Leave Application</h4>
						<button type="button" class="close" data-dismiss="modal">&times;</button>
					</div>
					<div class="modal-body">
						<input type="hidden" id="leave_id" name="leave_id" value="<?php echo $leaveDetails['leave_id']; ?>">
						<input type="hidden" id="action" name="action">
						
						<div class="form-group">
							<label for="admin_comments">Comments:</label>
							<textarea class="form-control" id="admin_comments" name="admin_comments" 
									  rows="4" placeholder="Add any comments or notes..." required></textarea>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
						<button type="submit" name="process_leave" class="btn btn-primary" id="confirmBtn">Confirm</button>
					</div>
				</form>
			</div>
		</div>
	</div>
	
	<script>
		function processLeave(leaveId, action) {
			document.getElementById('action').value = action;
			
			var title = action === 'approved' ? 'Approve Leave Application' : 'Reject Leave Application';
			var btnText = action === 'approved' ? 'Approve' : 'Reject';
			var btnClass = action === 'approved' ? 'btn-success' : 'btn-danger';
			
			document.getElementById('modalTitle').textContent = title;
			document.getElementById('confirmBtn').textContent = btnText;
			document.getElementById('confirmBtn').className = 'btn ' + btnClass;
			
			$('#processLeaveModal').modal('show');
		}
	</script>
</body>
</html>
